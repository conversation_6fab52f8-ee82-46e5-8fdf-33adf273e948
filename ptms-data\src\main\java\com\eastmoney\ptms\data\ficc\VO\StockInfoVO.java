package com.eastmoney.ptms.data.ficc.VO;

import com.eastmoney.digi.core.db.BasePo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * O32查询出来的最新持仓信息
 *
 * <AUTHOR>
 * @Date 2024/02/07 10:11
 */
@Getter
@Setter
@ToString
public class StockInfoVO extends BasePo {

    /**
     * 统计日期
     */
    private Integer lDate;

    /**
     * 基金编号 8020 || 8001
     */
    private Integer lFundId;


    /**
     * 证券代码
     */
    private String vcReportCode;

    /**
     * 证券名称
     */
    private String vcStockName;

    /**
     * 证券代码带后缀，例如  123354SZ
     */
    private String vcInterCode;

    /**
     * 证券类型，5-可转债 ，F-开放式基金
     */
    private String cStockType;
    /**
     * 最新持仓量
     */
    private BigDecimal lCurrentAmount;

    /**
     * 当日买量
     */
    private BigDecimal lBuyAmount;

    /**
     * 当日卖量
     */
    private BigDecimal lSaleAmount;


    /**
     * 当日买入金额
     */
    private BigDecimal enBuyBalance;


    /**
     * 当日卖出金额
     */
    private BigDecimal enSaleBalance;


    /**
     * 净买入金额
     */
    private BigDecimal enAbsBuyBalance;


    /**
     * 当日买入费用
     */
    private BigDecimal enBuyFee;


    /**
     * 结转过的期初成本 （前一日当前成本）
     */
    private BigDecimal enUntransferedInvest;


    /**
     * 百元债券利息
     */
    private BigDecimal enBondInterest;
}
