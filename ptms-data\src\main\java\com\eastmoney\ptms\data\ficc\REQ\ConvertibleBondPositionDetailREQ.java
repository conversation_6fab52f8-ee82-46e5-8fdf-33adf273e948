package com.eastmoney.ptms.data.ficc.REQ;

import com.eastmoney.digi.core.api.ApiRequest;

/**
 * <AUTHOR>
 * @date 2022-11-15 16:06
 */
public class ConvertibleBondPositionDetailREQ extends ApiRequest {

    //业务日期
    private Integer bizDate;

    //债券代码
    private String bondMarketCode;

    //O32基金编号 （8001 || 8020）
    private String fundId;

    public String getBondMarketCode() {
        return bondMarketCode;
    }

    public void setBondMarketCode(String bondMarketCode) {
        this.bondMarketCode = bondMarketCode;
    }


    public Integer getBizDate() {
        return bizDate;
    }

    public void setBizDate(Integer bizDate) {
        this.bizDate = bizDate;
    }

    public String getFundId() {
        return fundId;
    }

    public void setFundId(String fundId) {
        this.fundId = fundId;
    }
}
