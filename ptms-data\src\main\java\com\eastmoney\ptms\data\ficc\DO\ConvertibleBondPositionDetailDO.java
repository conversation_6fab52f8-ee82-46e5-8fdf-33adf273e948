package com.eastmoney.ptms.data.ficc.DO;

import java.math.BigDecimal;

/**
 * @description
 * <AUTHOR>
 * @version 1.0
 * @date 2022-11-10 14:16
 */
public class ConvertibleBondPositionDetailDO extends FiccBaseDO {

    /**
     * 业务日期
     */
    private Integer bizDate;

    /**
     * 债券代码带后缀
     */
    private String bondMarketCode;

    /**
     * 债券名称
     */
    private String bondName;

    /**
     * 基金编号
     */
    private Integer fundId;

    /**
     * 证券类型，5-可转债 ，F-开放式基金
     */
    private String stockType;

    /**
     * 最新持仓量  （可能为null）
     */
    private BigDecimal currentAmount;


    /**
     * 昨日持仓量  （可能为null）
     */
    private BigDecimal yesterdayAmount;

    /**
     * 当前市值   （可能为null）
     */
    private BigDecimal currentMarketValue;


    /**
     * 当日买量  （可能为null）
     */
    private BigDecimal buyAmount;

    /**
     * 当日卖量  （可能为null）
     */
    private BigDecimal saleAmount;


    /**
     * 昨收盘，4位小数  （可能为null）
     */
    private BigDecimal preClose;

    /**
     * 最新价，4位小数  （可能为null）
     */
    private BigDecimal close;


    /**
     * 实时涨跌，显示百分数，百分号后2位小数   （可能为null）
     */
    private BigDecimal ratio;

    /**
     * 实时盈亏   （可能为null）
     */
    private BigDecimal currentProfitAndLoss;

    /**
     * 债券评级
     */
    private String  BDRating;


    /**
     * 成本价（净价）
     */
    private BigDecimal costPrice;

    /**
     * 盈亏率（%）
     */
    private BigDecimal  profitAndLossRatio;

    public Integer getBizDate() {
        return bizDate;
    }

    public void setBizDate(Integer bizDate) {
        this.bizDate = bizDate;
    }

    public String getBondMarketCode() {
        return bondMarketCode;
    }

    public void setBondMarketCode(String bondMarketCode) {
        this.bondMarketCode = bondMarketCode;
    }

    public String getBondName() {
        return bondName;
    }

    public void setBondName(String bondName) {
        this.bondName = bondName;
    }

    public BigDecimal getCurrentAmount() {
        return currentAmount;
    }

    public void setCurrentAmount(BigDecimal currentAmount) {
        this.currentAmount = currentAmount;
    }

    public BigDecimal getYesterdayAmount() {
        return yesterdayAmount;
    }

    public void setYesterdayAmount(BigDecimal yesterdayAmount) {
        this.yesterdayAmount = yesterdayAmount;
    }

    public BigDecimal getCurrentMarketValue() {
        return currentMarketValue;
    }

    public void setCurrentMarketValue(BigDecimal currentMarketValue) {
        this.currentMarketValue = currentMarketValue;
    }

    public BigDecimal getBuyAmount() {
        return buyAmount;
    }

    public void setBuyAmount(BigDecimal buyAmount) {
        this.buyAmount = buyAmount;
    }

    public BigDecimal getSaleAmount() {
        return saleAmount;
    }

    public void setSaleAmount(BigDecimal saleAmount) {
        this.saleAmount = saleAmount;
    }

    public BigDecimal getPreClose() {
        return preClose;
    }

    public void setPreClose(BigDecimal preClose) {
        this.preClose = preClose;
    }

    public BigDecimal getClose() {
        return close;
    }

    public void setClose(BigDecimal close) {
        this.close = close;
    }

    public BigDecimal getRatio() {
        return ratio;
    }

    public void setRatio(BigDecimal ratio) {
        this.ratio = ratio;
    }

    public BigDecimal getCurrentProfitAndLoss() {
        return currentProfitAndLoss;
    }

    public void setCurrentProfitAndLoss(BigDecimal currentProfitAndLoss) {
        this.currentProfitAndLoss = currentProfitAndLoss;
    }

    public String getBDRating() {
        return BDRating;
    }

    public void setBDRating(String BDRating) {
        this.BDRating = BDRating;
    }

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public BigDecimal getProfitAndLossRatio() {
        return profitAndLossRatio;
    }

    public void setProfitAndLossRatio(BigDecimal profitAndLossRatio) {
        this.profitAndLossRatio = profitAndLossRatio;
    }

    public Integer getFundId() {
        return fundId;
    }

    public void setFundId(Integer fundId) {
        this.fundId = fundId;
    }

    public String getStockType() {
        return stockType;
    }

    public void setStockType(String stockType) {
        this.stockType = stockType;
    }

    @Override
    public String toString() {
        return "ConvertibleBondPositionDetailDO{" +
                "bizDate=" + bizDate +
                ", bondMarketCode='" + bondMarketCode + '\'' +
                ", bondName='" + bondName + '\'' +
                ", currentAmount=" + currentAmount +
                ", yesterdayAmount=" + yesterdayAmount +
                ", currentMarketValue=" + currentMarketValue +
                ", buyAmount=" + buyAmount +
                ", saleAmount=" + saleAmount +
                ", preClose=" + preClose +
                ", close=" + close +
                ", ratio=" + ratio +
                ", currentProfitAndLoss=" + currentProfitAndLoss +
                ", BDRating='" + BDRating + '\'' +
                '}';
    }
}
