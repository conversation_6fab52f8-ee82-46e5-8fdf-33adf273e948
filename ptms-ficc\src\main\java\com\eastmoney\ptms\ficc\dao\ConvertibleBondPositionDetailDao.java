package com.eastmoney.ptms.ficc.dao;

import com.eastmoney.ptms.dao.core.BaseDaoPtms;
import com.eastmoney.ptms.data.ficc.DO.ConvertibleBondPositionDetailDO;
import com.eastmoney.ptms.data.ficc.VO.ConvertibleBondVO;
import com.eastmoney.ptms.ficc.mapper.ConvertibleBondPositionDetailMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @createDate 2022-10-25 10:48
 * @description
 */
public class ConvertibleBondPositionDetailDao extends BaseDaoPtms<ConvertibleBondPositionDetailMapper, ConvertibleBondPositionDetailDO, String> {

    public final static ConvertibleBondPositionDetailDao Instance = new ConvertibleBondPositionDetailDao();

    private ConvertibleBondPositionDetailDao() {

    }

    public List<ConvertibleBondPositionDetailDO> queryHisRecords(Integer bizDate,String bondMarketCode,Integer fundId) {
        return callDbList(mapper -> mapper.queryHisRecords(bizDate,bondMarketCode,fundId));
    }

    public int mergeList(List<ConvertibleBondPositionDetailDO> list) {
        return callDb(mapper -> mapper.mergeList(list));
    }

    public List<ConvertibleBondVO> queryBond(Integer bizDate, String searchKey) {
        return callDbList(mapper -> mapper.queryBond(bizDate,searchKey));
    }
}
