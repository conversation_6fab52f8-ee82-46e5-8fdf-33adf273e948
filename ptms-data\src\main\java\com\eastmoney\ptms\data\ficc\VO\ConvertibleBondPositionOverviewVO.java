package com.eastmoney.ptms.data.ficc.VO;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 可转债损益报表-实时损益跟踪
 *
 * <AUTHOR>
 * @Date 2024/04/12 10:11
 */
@Getter
@Setter
@ToString
public class ConvertibleBondPositionOverviewVO {

    //以下是概览

    /**
     * 业务日期 yyyyMMdd
     */
    private Integer bizDate;


    /**
     * 最新市值（亿） 保留两位
     */
    private BigDecimal currentMarketValue;

    /**
     * ETF市值(亿) 保留两位
     */
    private BigDecimal etfMarketValue;

    /**
     * 债券市值(亿) 保留两位
     */
    private BigDecimal bondMarketValue;


    /**
     * 当日规模变动（亿）,今日最新市值汇总-昨日市值汇总
     */
    private BigDecimal changeMarketValue;


    /**
     * 中证转债（000832.SH） 实时涨跌，显示百分数，百分号后2位小数
     */
    private BigDecimal ratio;

    /**
     * 当日实时盈亏（万）,保留两位
     */
    private BigDecimal currentProfitAndLoss;


    /**
     * 当前持仓只数,持仓明细表中债券的总数
     */
    private Integer bondNum;

    /**
     * 持仓明细表中，实时涨跌为正数的债券数量
     */
    private Integer riseBondNum;

    /**
     * 持仓明细表中，实时涨跌为0的债券数量
     */
    private Integer flatBondNum;

    /**
     * 持仓明细表中，实时涨跌为负数的债券数量
     */
    private Integer fallBondNum;


    /**
     * 当日买量汇总-当日卖量汇总
     */
    private BigDecimal absBuyAmount;
}
