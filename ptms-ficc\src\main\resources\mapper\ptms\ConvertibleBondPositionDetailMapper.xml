<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.ptms.ficc.mapper.ConvertibleBondPositionDetailMapper">
    <insert id="mergeList">
        INSERT INTO
        ptms.ficc_convertible_bond_position_detail
        (EID,
         bizDate,
         bondMarketCode,
         bondName,
         fundId,
         stockType,
         currentAmount,
         currentMarketValue,
         yesterdayAmount,
         buyAmount,
         saleAmount,
         preClose,
         `close`,
         ratio,
         currentProfitAndLoss,
         BDRating,
         costPrice,
         profitAndLossRatio
         )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.EID},
            #{item.bizDate},
            #{item.bondMarketCode},
            #{item.bondName},
            #{item.fundId},
            #{item.stockType},
            #{item.currentAmount},
            #{item.currentMarketValue},
            #{item.yesterdayAmount},
            #{item.buyAmount},
            #{item.saleAmount},
            #{item.preClose},
            #{item.close},
            #{item.ratio},
            #{item.currentProfitAndLoss},
            #{item.BDRating},
            #{item.costPrice},
            #{item.profitAndLossRatio}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        EID=VALUES(EID),
        bizDate=VALUES(bizDate),
        fundId=VALUES(fundId),
        stockType=VALUES(stockType),
        bondMarketCode=VALUES(bondMarketCode),
        bondName=VALUES(bondName),
        currentAmount=VALUES(currentAmount),
        currentMarketValue=VALUES(currentMarketValue),
        yesterdayAmount=VALUES(yesterdayAmount),
        buyAmount=VALUES(buyAmount),
        saleAmount=VALUES(saleAmount),
        preClose=VALUES(preClose),
        close=VALUES(close),
        ratio=VALUES(ratio),
        currentProfitAndLoss=VALUES(currentProfitAndLoss),
        BDRating=VALUES(BDRating),
        costPrice=VALUES(costPrice),
        profitAndLossRatio=VALUES(profitAndLossRatio)
    </insert>

    <select id="queryHisRecords" resultType="com.eastmoney.ptms.data.ficc.DO.ConvertibleBondPositionDetailDO">
        select bondMarketCode,
        bondName,
        fundId,
        stockType,
        currentAmount,
        currentMarketValue,
        yesterdayAmount,
        buyAmount,
        saleAmount,
        preClose,
        `close`,
        ratio,
        currentProfitAndLoss,
        BDRating,
        costPrice,
        profitAndLossRatio
        from ficc_convertible_bond_position_detail
        <where>
            <if test="bizDate != null">
                and bizDate = #{bizDate}
            </if>
            <if test="bondMarketCode!=null and bondMarketCode!=''">
                and bondMarketCode = #{bondMarketCode}
            </if>
            <if test="fundId!=null">
                and fundId = #{fundId}
            </if>
        </where>
    </select>
    <select id="queryBond" resultType="com.eastmoney.ptms.data.ficc.VO.ConvertibleBondVO">
        select
        bondMarketCode,
        bondName
        from ficc_convertible_bond_position_detail
        <where>
            <if test="bizDate != null">
                and bizDate = #{bizDate}
            </if>
            <if test="searchKey!=null and searchKey!=''">
                and (bondMarketCode like concat('%', #{searchKey}, '%') or bondName  like concat('%', #{searchKey}, '%'))
            </if>
        </where>
    </select>
</mapper>