package com.eastmoney.ptms.ficc.job;

import com.eastmoney.digi.common.util.TimeTool;
import com.eastmoney.digi.core.err.exception.DigiException;
import com.eastmoney.ptms.ficc.choice.IBTradeDateService;
import com.eastmoney.ptms.ficc.service.O32ConvertibleBondService;
import com.eastmoney.ptms.ficc.tool.log.LogTool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @ClassName RedisCacheJob
 * @Description
 * @date 2024/1/4
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class RedisCacheJob {

    private final O32ConvertibleBondService o32ConvertibleBondService;

    private final IBTradeDateService ibTradeDateService;

    @Value("${o32.enable:true}")
    private Boolean O32Enable;

    /**
     * 每分钟刷新一次O32的 可转债、基金编号8020,8001的持仓 （ [证券类型]=5-可转债 || [证券类型] = F-开放式基金&[证券代码] = 511380、511180 ）
     * 启动执行
     */
    @Scheduled(cron = "0 0/1 9-16 * * ? ")
    @PostConstruct
    public void refreshConvertibleBondAmount() {
        try {
            String date = TimeTool.CURRENT.getCunrrentyyyyMMdd();
            if (O32Enable && ibTradeDateService.isTradeDate(Integer.valueOf(date))) {
                o32ConvertibleBondService.refreshConvertibleBondAmount();
                LogTool.info("每分钟刷新O32的可转债持仓到redis完成");
            }
        } catch (DigiException e) {
            LogTool.info("每分钟刷新O32的可转债持仓到redis，未获取到分布式锁");
        } catch (Exception e) {
            LogTool.error("每分钟刷新O32的可转债持仓到redis异常", e);
        }
    }

    /**
     * 定时任务归档 可转债持仓明细+实时损益报表
     */
    @Scheduled(cron = "0 15 23 * * ? ")
    public void saveTodayConvertibleBondPosition() {
        try {
            String date = TimeTool.CURRENT.getCunrrentyyyyMMdd();
            if (O32Enable && ibTradeDateService.isTradeDate(Integer.valueOf(date))) {
                o32ConvertibleBondService.saveTodayConvertibleBondPosition();
            }
        } catch (DigiException e) {
            LogTool.info("每日可转债持仓落库，未获取到分布式锁");
        } catch (Exception e) {
            LogTool.error("每日可转债持仓落异常", e);
        }
    }


    /**
     * 从O32获取昨日持仓
     * 启动执行
     */
    @Scheduled(cron = "0 0 6,8 * * ? ")
    @PostConstruct
    public void getYesterdayConvertibleBondAmount2Redis() {
        try {
            Integer yyyyMMdd = Integer.parseInt(TimeTool.CURRENT.getCunrrentyyyyMMdd());
            Integer lastBizDateOfXSHG = o32ConvertibleBondService.getLastBizDateOfXSHG(yyyyMMdd);
            if (O32Enable) {
                o32ConvertibleBondService.getMarketValueOfLastBizDate(lastBizDateOfXSHG);
                o32ConvertibleBondService.getLastBizDatePositionDB(lastBizDateOfXSHG);
                o32ConvertibleBondService.getYesterdayConvertibleBondAmount2Redis(lastBizDateOfXSHG);
            }
        } catch (DigiException e) {
            LogTool.info("从O32获取昨日持仓到redis，未获取到分布式锁");
        } catch (Exception e) {
            LogTool.error("从O32获取昨日持仓异常", e);
        }
    }
}
