package com.eastmoney.ptms.data.ficc.DO;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2022-11-10 14:16
 */

public class ConvertibleBondPositionOverviewDO extends FiccBaseDO {

    /**
     * 业务日期 yyyyMMdd
     */
    private Integer bizDate;


    /**
     * 最新市值（亿）
     */
    private BigDecimal currentMarketValue;

    /**
     * etf市值（亿）
     */
    private BigDecimal etfMarketValue;

    /**
     * 债券市值（亿）
     */
    private BigDecimal bondMarketValue;

    /**
     * 当日规模变动（亿）,今日最新市值汇总-昨日市值汇总
     */
    private BigDecimal changeMarketValue;

    /**
     * 当日买量汇总-当日卖量汇总
     */
    private BigDecimal absBuyAmount;


    /**
     * 中证转债（000832.SH） 实时涨跌，显示百分数，百分号后2位小数
     */
    private BigDecimal ratio;

    /**
     * 当日实时盈亏
     */
    private BigDecimal currentProfitAndLoss;

    /**
     * 当前持仓只数,持仓明细表中债券的总数
     */
    private Integer bondNum;

    /**
     * 持仓明细表中，实时涨跌为正数的债券数量
     */
    private Integer riseBondNum;

    /**
     * 持仓明细表中，实时涨跌为0的债券数量
     */
    private Integer flatBondNum;

    /**
     * 持仓明细表中，实时涨跌为负数的债券数量
     */
    private Integer fallBondNum;

    public Integer getBizDate() {
        return bizDate;
    }

    public void setBizDate(Integer bizDate) {
        this.bizDate = bizDate;
    }

    public BigDecimal getCurrentMarketValue() {
        return currentMarketValue;
    }

    public void setCurrentMarketValue(BigDecimal currentMarketValue) {
        this.currentMarketValue = currentMarketValue;
    }

    public BigDecimal getAbsBuyAmount() {
        return absBuyAmount;
    }

    public void setAbsBuyAmount(BigDecimal absBuyAmount) {
        this.absBuyAmount = absBuyAmount;
    }

    public BigDecimal getRatio() {
        return ratio;
    }

    public void setRatio(BigDecimal ratio) {
        this.ratio = ratio;
    }

    public BigDecimal getCurrentProfitAndLoss() {
        return currentProfitAndLoss;
    }

    public void setCurrentProfitAndLoss(BigDecimal currentProfitAndLoss) {
        this.currentProfitAndLoss = currentProfitAndLoss;
    }

    public Integer getBondNum() {
        return bondNum;
    }

    public void setBondNum(Integer bondNum) {
        this.bondNum = bondNum;
    }

    public Integer getRiseBondNum() {
        return riseBondNum;
    }

    public void setRiseBondNum(Integer riseBondNum) {
        this.riseBondNum = riseBondNum;
    }

    public Integer getFlatBondNum() {
        return flatBondNum;
    }

    public void setFlatBondNum(Integer flatBondNum) {
        this.flatBondNum = flatBondNum;
    }

    public Integer getFallBondNum() {
        return fallBondNum;
    }

    public void setFallBondNum(Integer fallBondNum) {
        this.fallBondNum = fallBondNum;
    }

    public BigDecimal getChangeMarketValue() {
        return changeMarketValue;
    }

    public void setChangeMarketValue(BigDecimal changeMarketValue) {
        this.changeMarketValue = changeMarketValue;
    }

    public BigDecimal getEtfMarketValue() {
        return etfMarketValue;
    }

    public void setEtfMarketValue(BigDecimal etfMarketValue) {
        this.etfMarketValue = etfMarketValue;
    }

    public BigDecimal getBondMarketValue() {
        return bondMarketValue;
    }

    public void setBondMarketValue(BigDecimal bondMarketValue) {
        this.bondMarketValue = bondMarketValue;
    }

}
