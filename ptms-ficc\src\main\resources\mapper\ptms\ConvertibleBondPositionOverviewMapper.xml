<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.eastmoney.ptms.ficc.mapper.ConvertibleBondPositionOverviewMapper">


    <insert id="merge" parameterType="com.eastmoney.ptms.data.ficc.DO.ConvertibleBondPositionOverviewDO">
        insert into ficc_convertible_bond_position_overview
        (
        EID
        <if test="del != null">
            ,del
        </if>
        <if test="bizDate != null">
            ,bizDate
        </if>
        <if test="currentMarketValue != null">
            ,currentMarketValue
        </if>
        <if test="etfMarketValue != null">
            ,etfMarketValue
        </if>
        <if test="bondMarketValue != null">
            ,bondMarketValue
        </if>
        <if test="changeMarketValue != null">
            ,changeMarketValue
        </if>
        <if test="currentProfitAndLoss != null">
            ,currentProfitAndLoss
        </if>
        <if test="ratio != null">
            ,ratio
        </if>
        <if test="bondNum != null">
            ,bondNum
        </if>
        <if test="riseBondNum != null">
            ,riseBondNum
        </if>
        <if test="flatBondNum != null">
            ,flatBondNum
        </if>
        <if test="fallBondNum != null">
            ,fallBondNum
        </if>
        )values(
        #{EID}
        <if test="del != null">
            ,#{del}
        </if>
        <if test="bizDate != null">
            ,#{bizDate}
        </if>
        <if test="currentMarketValue != null">
            ,#{currentMarketValue}
        </if>
        <if test="etfMarketValue != null">
            ,#{etfMarketValue}
        </if>
        <if test="bondMarketValue != null">
            ,#{bondMarketValue}
        </if>
        <if test="changeMarketValue != null">
            ,#{changeMarketValue}
        </if>
        <if test="currentProfitAndLoss != null">
            ,#{currentProfitAndLoss}
        </if>
        <if test="ratio != null">
            ,#{ratio}
        </if>
        <if test="bondNum != null">
            ,#{bondNum}
        </if>
        <if test="riseBondNum != null">
            ,#{riseBondNum}
        </if>
        <if test="flatBondNum != null">
            ,#{flatBondNum}
        </if>
        <if test="fallBondNum != null">
            ,#{fallBondNum}
        </if>
        )
        ON DUPLICATE KEY UPDATE
        EID=VALUES(EID)
        <if test="del != null">
            ,del=VALUES(del)
        </if>
        <if test="bizDate != null">
            ,bizDate=VALUES(bizDate)
        </if>
        <if test="currentMarketValue != null">
            ,currentMarketValue=VALUES(currentMarketValue)
        </if>
        <if test="etfMarketValue != null">
            ,etfMarketValue=VALUES(etfMarketValue)
        </if>
        <if test="bondMarketValue != null">
            ,bondMarketValue=VALUES(bondMarketValue)
        </if>
        <if test="changeMarketValue != null">
            ,changeMarketValue=VALUES(changeMarketValue)
        </if>
        <if test="currentProfitAndLoss != null">
            ,currentProfitAndLoss=VALUES(currentProfitAndLoss)
        </if>
        <if test="ratio != null">
            ,ratio=VALUES(ratio)
        </if>
        <if test="bondNum != null">
            ,bondNum=VALUES(bondNum)
        </if>
        <if test="riseBondNum != null">
            ,riseBondNum=VALUES(riseBondNum)
        </if>
        <if test="flatBondNum != null">
            ,flatBondNum=VALUES(flatBondNum)
        </if>
        <if test="fallBondNum != null">
            ,fallBondNum=VALUES(fallBondNum)
        </if>
    </insert>

    <select id="queryHisRecord" resultType="com.eastmoney.ptms.data.ficc.DO.ConvertibleBondPositionOverviewDO">
        select EID, EITIME, EUTIME, del, bizDate, currentMarketValue, etfMarketValue,bondMarketValue,changeMarketValue,absBuyAmount, currentProfitAndLoss, ratio, bondNum, riseBondNum, flatBondNum, fallBondNum from ficc_convertible_bond_position_overview
        where bizDate = #{bizDate} and del = 0
        order by EITIME desc limit 1
    </select>

</mapper>