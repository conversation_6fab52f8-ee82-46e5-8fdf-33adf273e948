#==============================================
# application
#==============================================
server.port=10080
logging.config= classpath:config/logback.xml

app.appId=em-ptms-service
app.version=1.0.0
app.serverId=0
app.modules=com.eastmoney.ptms.api.core.ApiModule
app.ompProdId=213000156487312987
app.debugFlag=false
app.captchaPath=false
#==============================================
# spring
#==============================================
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB
spring.session.store-type=redis
#\u5F00\u53D1\u7EBF
spring.data.mongodb.uri=********************************************************
#\u6D4B\u8BD5\u7EBF
#spring.data.mongodb.uri=********************************************************

#===============================================
# spring.redis \u5F00\u53D1\u7EBF
#===============================================
spring.redis.host=*************
spring.redis.port=6379
spring.redis.password=ytuhUK5pbr
spring.redis.timeout=20000
spring.redis.database=0
spring.redis.lettuce.pool.max-active=8
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=0
spring.redis.lettuce.pool.max-wait=-1
spring.redis.sentinel.master=mymaster
spring.redis.sentinel.nodes=*************:26379,*************:26379,*************:26380

spring.pmms.redis.password=wKMkd6ew49
spring.pmms.redis.timeout=30000
spring.pmms.redis.database=8
spring.pmms.redis.lettuce.pool.max-active=32
spring.pmms.redis.lettuce.pool.max-idle=32
spring.pmms.redis.lettuce.pool.min-idle=0
spring.pmms.redis.lettuce.pool.max-wait=-1
spring.pmms.redis.sentinel.master=mymaster
spring.pmms.redis.sentinel.nodes=*************:26380,*************:26381,*************:26382
#===============================================
# spring.redis \u6D4B\u8BD5\u7EBF
#===============================================
#spring.redis.password=wKMkd6ew49
#spring.redis.timeout=30000
#spring.redis.database=0
#spring.redis.lettuce.pool.max-active=8
#spring.redis.lettuce.pool.max-idle=8
#spring.redis.lettuce.pool.min-idle=0
#spring.redis.lettuce.pool.max-wait=-1
#spring.redis.sentinel.master=mymaster
#spring.redis.sentinel.nodes=*************:26380,*************:26381,*************:26382

#===============================================
# ptms-hub
#===============================================
hub.http.url=http://*************:10091
hub.http.ipWhiteList=

#===============================================
# \u5BA2\u6237\u7BA1\u7406
#===============================================
cust.localFilePath = D:/temp
#===============================================
# \u56FA\u6536
#===============================================
ht.url=http://************:8081/api
ht.client.userid=admin1
#ht.client.userid=15001
#ht.client.pwd=KpvtJCo5IQI=
ht.client.pwd=e6VrtQE3tgyR/xQRSRhjjg==
ht.client.default.manager=17210
#===============================================
# choice
#===============================================
choice.bondChoiceService.enabled=true
#===============================================
# \u81EA\u8425\u56FA\u6536\u7CFB\u7EDFurl
#===============================================
ptms.service.host=http://localhost:20080/#/fixed-income
#===============================================
# thymeleaf\u6A21\u677F\u5F15\u64CE\u914D\u7F6E
#===============================================
spring.thymeleaf.prefix=classpath:config/templates/
#===============================================
# \u62A5\u8868\u901A\u7528\u914D\u7F6E\u987A\u5E8F\u914D\u7F6E
#===============================================
report.config.departmentName.order=\u56FA\u6536\u6295\u8D44,\u4E8C\u7EA7\u8D44\u91D1\u6C60,\u9500\u552E\u4EA4\u6613
report.config.groupName.order=\u4E3B\u8D26\u53F7,\u5229\u7387\u6295\u8D44,\u505A\u5E02\u4EA4\u6613,\u53EF\u8F6C\u503A\u4EA4\u6613,\u6C11\u751F\u52A0\u94F6,\u56FD\u541B\u8D44\u7BA1,\u4E8C\u7EA7\u8D44\u91D1\u6C60,\u9500\u552E\u4EA4\u6613
#===============================================
# \u6295\u8D44\u7ECF\u7406\u865A\u62DF\u6301\u4ED3\u8D28\u62BC\u4E13\u6237
#===============================================
position.manager.distribution.name=\u878D\u8D44\u4E13\u6237-\u9AD8\u626C\u6D4B\u8BD5
#===============================================
# \u6295\u8D44\u7ECF\u7406\u865A\u62DF\u6301\u4ED3\u6392\u9664\u8D26\u6237
#===============================================
position.manager.distribution.exclude=\u5229\u7387\u503A\u4EA4\u6613\u6237-\u9AD8\u626C\u6D4B\u8BD5
#===============================================
# \u8D44\u7BA1\u8BA1\u5212\u5185\u90E8\u8BC1\u5238\u8D26\u6237
#===============================================
report.pal.detail.exclude.insideSecAccName=\u62A5\u4EF7\u56DE\u8D2D-\u6C11\u751F\u52A0\u94F6\u8D22\u5BCC1\u53F7\u6D4B\u8BD5,\u56FD\u6CF0\u541B\u5B89\u541B\u5F973409\u53F7\u6D4B\u8BD5,\u56FD\u6CF0\u541B\u5B89\u5F973485\u53F7\u6D4B\u8BD5
#===============================================
#\u8D22\u52A1\u8D44\u91D1\u6C60\u8D28\u62BC\u5F0F\u56DE\u8D2D\u4E13\u6237
#===============================================
position.manager.distribution.financial.name=\u878D\u8D44\u4E13\u6237-\u81EA\u8425\u6295\u8D44\u501F\u5238\u878D\u8D44\u6D4B\u8BD5
#===============================================
#\u8D22\u52A1\u8D44\u91D1\u6C60\u8282\u70B9\u4E0B\u5185\u90E8\u8BC1\u5238\u8D26\u6237
#===============================================
financial.department.list=\u8D22\u52A1\u8D44\u91D1\u6D4B\u8BD5,\u878D\u5238\u5356\u7A7A-\u8D22\u52A1\u8D44\u91D1\u6D4B\u8BD5,\u878D\u5238\u4E13\u6237-\u8D22\u52A1\u8D44\u91D1\u6D4B\u8BD5,\u878D\u8D44\u4E13\u6237-\u8D22\u52A1\u8D44\u91D1\u6D4B\u8BD5,\u878D\u8D44\u4E13\u6237-\u81EA\u8425\u6295\u8D44\u501F\u5238\u878D\u8D44\u6D4B\u8BD5


pmms.service.ip=************
pmms.service.httpPort=18080
pmms.service.quoteSnapshotPath=/serviceApi/api/shsz/getQuoteSnapshot

o32.enable=true

o32.fund-id=8020,8001

#===============================================
#\u56DE\u8D2D\u504F\u79BB\u5EA6\u660E\u7EC6\u8FC7\u6EE4\u62A5\u8868\u901A\u7528\u914D\u7F6E\u6240\u5C5E\u79D1\u76EE
#===============================================
back.offset.detail.filter.report.config.subject=\u4EA7\u54C1\u6237

#===============================================
#\u4FE1\u8BC4\u4E0E\u6295\u8D44\u7ECF\u7406
#===============================================
credit.rating.and.invest.manger=90008

pmms.client.link-url-host=https://ceshi.securities.eastmoney.com:7438/zqhdweb/marketMaker/marketMaker.html

ddms.enable=true

ccdcStatus.redis.waitSeconds=10

ccdcStatus.delay.time=10

ccdcStatus.handleAmpInfo.enable=true

report.cisp.f3070.bizMonth=202411
report.cisp.f3070.endBalance.03=0
report.cisp.f3070.endBalance.0301=0
report.cisp.f3070.endBalance.030101=0
report.cisp.f3070.endBalance.030102=0
report.cisp.f3070.endBalance.0302=0
report.cisp.f3070.endBalance.030201=0
report.cisp.f3070.endBalance.030202=0
report.cisp.f3070.yearTotalIncrease.03=0
report.cisp.f3070.yearTotalIncrease.0301=0
report.cisp.f3070.yearTotalIncrease.030101=0
report.cisp.f3070.yearTotalIncrease.030102=0
report.cisp.f3070.yearTotalIncrease.0302=0
report.cisp.f3070.yearTotalIncrease.030201=0
report.cisp.f3070.yearTotalIncrease.030202=0

our-side.tradeMemberIds=301455
