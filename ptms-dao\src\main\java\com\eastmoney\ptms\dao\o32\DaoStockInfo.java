package com.eastmoney.ptms.dao.o32;

import com.eastmoney.ptms.dao.core.BaseDaoO32;
import com.eastmoney.ptms.dao.mapper.o32.O32StockInfoMapper;
import com.eastmoney.ptms.data.ficc.VO.StockInfoVO;
import com.eastmoney.ptms.data.po.o32.POStockInfo;

import java.util.List;

/**
 * <AUTHOR>
 */

public class DaoStockInfo extends BaseDaoO32<O32StockInfoMapper, POStockInfo, String> {
    //region singleton
    public final static DaoStockInfo Instance = new DaoStockInfo();

    private DaoStockInfo() {
    }
    //endreigon


    /**
     * 查询 O32可转债 基金编号 fundId  当日持仓
     */
    public List<StockInfoVO> selectConvertibleBondAmount(List<String> fundIdList) {
        return callDb(mapper -> mapper.selectConvertibleBondAmount(fundIdList));
    }


    /**
     * 查询 O32可转债 基金编号 fundId  特定日期的历史持仓
     */
    public List<StockInfoVO> selectYesterdayConvertibleBondAmount(Integer lastBizDateOfXSHG, List<String> fundIdList) {
        return callDb(mapper -> mapper.selectYesterdayConvertibleBondAmount(lastBizDateOfXSHG, fundIdList));
    }

}