package com.eastmoney.ptms.ficc.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.eastmoney.digi.common.util.BigDecimalUtil;
import com.eastmoney.digi.common.util.ConvertUtil;
import com.eastmoney.digi.common.util.RandomUtil;
import com.eastmoney.digi.common.util.TimeTool;
import com.eastmoney.digi.core.api.ApiResponse;
import com.eastmoney.digi.core.api.ApiUtil;
import com.eastmoney.digi.core.api.EmSort;
import com.eastmoney.digi.core.enumm.LockFailAction;
import com.eastmoney.digi.core.json.GsonUtil;
import com.eastmoney.digi.core.redis.RedisUtil;
import com.eastmoney.digi.core.redis.lock.RedisLock;
import com.eastmoney.framework.json.JsonUtil;
import com.eastmoney.ptms.dao.o32.DaoStockInfo;
import com.eastmoney.ptms.data.enums.EnumDel;
import com.eastmoney.ptms.data.enums.NumberUnitEnum;
import com.eastmoney.ptms.data.ficc.BO.BondValuationParam;
import com.eastmoney.ptms.data.ficc.DO.ConvertibleBondPositionDetailDO;
import com.eastmoney.ptms.data.ficc.DO.ConvertibleBondPositionOverviewDO;
import com.eastmoney.ptms.data.ficc.DTO.choice.BondValuationRespDTO;
import com.eastmoney.ptms.data.ficc.REQ.ConvertibleBondPositionDetailREQ;
import com.eastmoney.ptms.data.ficc.REQ.ConvertibleBondREQ;
import com.eastmoney.ptms.data.ficc.VO.ConvertibleBondAllInfoVO;
import com.eastmoney.ptms.data.ficc.VO.ConvertibleBondMarketDataVO;
import com.eastmoney.ptms.data.ficc.VO.ConvertibleBondPositionDetailVO;
import com.eastmoney.ptms.data.ficc.VO.ConvertibleBondPositionOverviewVO;
import com.eastmoney.ptms.data.ficc.VO.ConvertibleBondRatingVO;
import com.eastmoney.ptms.data.ficc.VO.ConvertibleBondVO;
import com.eastmoney.ptms.data.ficc.VO.StockInfoVO;
import com.eastmoney.ptms.ficc.choice.BondChoiceService;
import com.eastmoney.ptms.ficc.choice.IBTradeDateService;
import com.eastmoney.ptms.ficc.common.CommonConstant;
import com.eastmoney.ptms.ficc.config.PmmsServiceProperties;
import com.eastmoney.ptms.ficc.dao.ConvertibleBondPositionDetailDao;
import com.eastmoney.ptms.ficc.dao.ConvertibleBondPositionOverviewDao;
import com.eastmoney.ptms.ficc.enums.BDRatingEnum;
import com.eastmoney.ptms.ficc.enums.HtMarketEnum;
import com.eastmoney.ptms.ficc.enums.TradeMarketCodeEnum;
import com.eastmoney.ptms.ficc.tool.log.LogTool;
import com.google.gson.reflect.TypeToken;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description： 可转债 持仓信息查询
 * @author: caoyang
 * @since: 2024/2/7 11:02
 * @version: 1.0
 */
@Service
@RequiredArgsConstructor
public class O32ConvertibleBondService {


    private final IBTradeDateService ibTradeDateService;

    /**
     * 业务日期 ：上一交易所交易日
     */
    private static final Map<Integer, Integer> MARKET_LAST_BIZ_DATE = new ConcurrentHashMap<>();

    /**
     * 业务日期_债券代码 ： 持仓量
     * 获取昨日持仓量
     */
    private static final Map<String, BigDecimal> YESTERDAY_AMOUNT = new ConcurrentHashMap<>();

    AtomicReference<BigDecimal> YESTERDAY_MARKET_VALUE = new AtomicReference<>();

    private final BondChoiceService bondChoiceService;

    private final String CHOICE_VALUE_TYPE = "1";

    /**
     * choice查询清算类型
     */
    private final String CHOICE_CLEAR_TYPE = "1";

    /**
     * 证券类型-可转债
     */
    private final String stock_type_bond = "5";

    /**
     * 证券类型-eft
     */
    private final String stock_type_etf = "F";

    /**
     * choice查询资金类型
     */
    private final BigDecimal CHOICE_BOND_PRICE = BigDecimal.ONE;

    /**
     * 业务日期 ： 数据库中的昨日持仓
     * 为了获取昨日持仓量
     */
    private static final Map<Integer, List<ConvertibleBondPositionDetailDO>> LAST_BIZDATE_POSITION_DB_MAP = new ConcurrentHashMap<>();

    private static final List<String> CSI_CONVERTIBLE_BOND_List = Collections.singletonList("000832.SH");

    private static final Map<String, Function<ConvertibleBondPositionDetailDO, Comparable>> SORT_FIELD_FUNCTION = new LinkedHashMap<>();
    private static final Map<String, Comparator<ConvertibleBondPositionDetailDO>> ASC_SORT_MAP = new LinkedHashMap<>();
    private static final Map<String, Comparator<ConvertibleBondPositionDetailDO>> DESC_SORT_MAP = new LinkedHashMap<>();


    @Value("${o32.enable:true}")
    private Boolean O32Enable;

    @Value("#{'${o32.fund-id:8020,8001}'.split(',')}")
    private List<String> o32FundIdList;

    static {
        SORT_FIELD_FUNCTION.put("bondMarketCode", ConvertibleBondPositionDetailDO::getBondMarketCode);
        SORT_FIELD_FUNCTION.put("bondName", ConvertibleBondPositionDetailDO::getBondName);
        SORT_FIELD_FUNCTION.put("fundId", ConvertibleBondPositionDetailDO::getFundId);
        SORT_FIELD_FUNCTION.put("currentAmount", ConvertibleBondPositionDetailDO::getCurrentAmount);
        SORT_FIELD_FUNCTION.put("yesterdayAmount", ConvertibleBondPositionDetailDO::getYesterdayAmount);
        SORT_FIELD_FUNCTION.put("currentMarketValue", ConvertibleBondPositionDetailDO::getCurrentMarketValue);
        SORT_FIELD_FUNCTION.put("buyAmount", ConvertibleBondPositionDetailDO::getBuyAmount);
        SORT_FIELD_FUNCTION.put("saleAmount", ConvertibleBondPositionDetailDO::getSaleAmount);
        SORT_FIELD_FUNCTION.put("preClose", ConvertibleBondPositionDetailDO::getPreClose);
        SORT_FIELD_FUNCTION.put("close", ConvertibleBondPositionDetailDO::getClose);
        SORT_FIELD_FUNCTION.put("ratio", ConvertibleBondPositionDetailDO::getRatio);
        SORT_FIELD_FUNCTION.put("currentProfitAndLoss", ConvertibleBondPositionDetailDO::getCurrentProfitAndLoss);
        SORT_FIELD_FUNCTION.put("BDRating", ConvertibleBondPositionDetailDO::getBDRating);
        SORT_FIELD_FUNCTION.put("costPrice", ConvertibleBondPositionDetailDO::getCostPrice);
        SORT_FIELD_FUNCTION.put("profitAndLossRatio", ConvertibleBondPositionDetailDO::getProfitAndLossRatio);


        SORT_FIELD_FUNCTION.forEach((key, value) -> {
            ASC_SORT_MAP.put(key, Comparator.comparing(value, Comparator.nullsLast(Comparator.naturalOrder())));
            DESC_SORT_MAP.put(key, Comparator.comparing(value, Comparator.nullsLast(Comparator.reverseOrder())));
        });

    }

    /**
     * 查询 O32可转债 基金编号8020 当日持仓
     */
    public List<StockInfoVO> selectConvertibleBondAmount() {
        if (!O32Enable) {
            //准上
            return Collections.emptyList();
        }
        try {
            return DaoStockInfo.Instance.selectConvertibleBondAmount(o32FundIdList);
        } catch (Exception e) {
            LogTool.error("o32可转债当日持仓数据查询出错,", e);
            return Collections.emptyList();
        }
    }


    @RedisLock(key = "getYesterdayConvertibleBondAmount2Redis", expire = 60000, action = LockFailAction.GIVE_UP)
    public void getYesterdayConvertibleBondAmount2Redis(Integer lastBizDateOfXSHG) {
        List<StockInfoVO> stockInfoVOS;
        //查询 O32可转债 基金编号8020 昨日持仓
        if (!O32Enable) {
            //准上
            stockInfoVOS = Collections.emptyList();
        } else {
            try {
                stockInfoVOS = DaoStockInfo.Instance.selectYesterdayConvertibleBondAmount(lastBizDateOfXSHG, o32FundIdList);
            } catch (Exception e) {
                LogTool.error("o32可转债上一交易日持仓数据查询出错,", e);
                stockInfoVOS = Collections.emptyList();
            }
        }
        RedisUtil.opsForValue().set(CommonConstant.PREFIX_YESTERDAY_CONVERTIBLE_BOND_AMOUNT, JsonUtil.toJson(stockInfoVOS), CommonConstant.O32_CACHE_TIME, TimeUnit.HOURS);
        LogTool.info("从O32获取上一交易日持仓到redis完成,上一交易所交易日：{}", lastBizDateOfXSHG);
    }

    @RedisLock(key = "refreshConvertibleBondAmount", expire = 60000, action = LockFailAction.GIVE_UP)
    public void refreshConvertibleBondAmount() {
        List<StockInfoVO> stockInfoVOS = selectConvertibleBondAmount();
        RedisUtil.opsForValue().set(CommonConstant.PREFIX_CONVERTIBLE_BOND_AMOUNT, JsonUtil.toJson(stockInfoVOS), CommonConstant.O32_CACHE_TIME, TimeUnit.HOURS);
    }

    /**
     * 归档 可转债持仓明细+实时损益报表
     *
     * @return boolean
     */
    @RedisLock(key = "saveTodayConvertibleBondPosition", expire = 60000, action = LockFailAction.GIVE_UP)
    public void saveTodayConvertibleBondPosition() {
        Integer yyyyMMdd = Integer.parseInt(TimeTool.CURRENT.getCunrrentyyyyMMdd());
        //当天不是交易所交易日 不落库
        if (!ibTradeDateService.isTradeDateWithMarketCode(yyyyMMdd, TradeMarketCodeEnum.XSHG)) {
            LogTool.info("不是交易日，可转债持仓不落库");
            return;
        }
        ConvertibleBondPositionDetailREQ detailREQ = new ConvertibleBondPositionDetailREQ();
        detailREQ.setBizDate(Integer.parseInt(TimeTool.CURRENT.getCunrrentyyyyMMdd()));
        detailREQ.setPageNo(1);
        detailREQ.setPageSize(9999999);
        ApiResponse<ConvertibleBondPositionDetailDO> apiResponse = selectConvertibleBondPositionDetailDO(detailREQ, false);
        List<ConvertibleBondPositionDetailDO> detailDOS = apiResponse.getData();
        detailDOS.forEach(vo -> {
            vo.setBizDate(yyyyMMdd);
            vo.setEID(RandomUtil.randomUUID());
        });
        int totalSize = detailDOS.size();
        int num = 0;
        int batchSize = 1000;
        for (int i = 0; i < totalSize; i += batchSize) {
            int endIndex = Math.min(i + batchSize, totalSize);
            List<ConvertibleBondPositionDetailDO> batchList = detailDOS.subList(i, endIndex);
            ConvertibleBondPositionDetailDao.Instance.mergeList(batchList);
            LogTool.info("[可转债持仓明细]第{}次插入(更新)，插入区间范围：{}~{}", num++, i, endIndex);
        }
        // 计算实时损益
        ConvertibleBondPositionOverviewDO convertibleBondPositionOverviewDO = calPositionOverview(detailDOS);
        ConvertibleBondPositionOverviewDao.Instance.merge(convertibleBondPositionOverviewDO);
        LogTool.info("每日可转债持仓明细、实时损益落库完成");
    }

    /**
     * 查询 可转债损益报表-持仓明细
     */
    public ApiResponse<ConvertibleBondPositionDetailVO> selectConvertibleBondPositionDetail(ConvertibleBondPositionDetailREQ req) {
        ApiResponse<ConvertibleBondPositionDetailVO> response = new ApiResponse<>();
        //设置精度
        ApiResponse<ConvertibleBondPositionDetailDO> positionDetailDOResponse = selectConvertibleBondPositionDetailDO(req, true);
        List<ConvertibleBondPositionDetailVO> vos = new ArrayList<>();
        CollUtil.emptyIfNull(positionDetailDOResponse.getData()).forEach(detailDO -> {
            ConvertibleBondPositionDetailVO convertibleBondPositionDetailVO = ConvertibleBondPositionDetailVO.fromDetailDO(detailDO);
            vos.add(convertibleBondPositionDetailVO);
        });
        return response.setData(vos).setCount(positionDetailDOResponse.getCount()).setOk();
    }

    private static boolean nowIsBefore930() {
        Calendar calendar = Calendar.getInstance();
        int minutes = calendar.get(Calendar.MINUTE);
        int hours = calendar.get(Calendar.HOUR_OF_DAY);
        return hours < 9 || (hours == 9 && minutes < 30);
    }

    /**
     * @param req      查询参数
     * @param addTotal 是否将汇总行放到每页的最后
     * @return
     */
    private ApiResponse<ConvertibleBondPositionDetailDO> selectConvertibleBondPositionDetailDO(ConvertibleBondPositionDetailREQ req, boolean addTotal) {
        ApiResponse<ConvertibleBondPositionDetailDO> response = new ApiResponse<>();
        if (!O32Enable) {
            return response.setData(Collections.emptyList()).setOk();
        }
        Integer yyyyMMdd = Integer.parseInt(TimeTool.CURRENT.getCunrrentyyyyMMdd());
        Integer bizDate = req.getBizDate();
        Integer pageNo = req.getPageNo();
        Integer pageSize = req.getPageSize();
        String bondMarketCode = req.getBondMarketCode();
        Integer fundId;
        if(StrUtil.isNotBlank(req.getFundId()) && NumberUtil.isInteger(req.getFundId())){
            fundId = Integer.parseInt(req.getFundId());
        } else {
            fundId = null;
        }
        List<EmSort> querySorts = new ArrayList<>();
        //添加默认排序
        if (CollUtil.isEmpty(req.getSorts())) {
            EmSort sort = new EmSort("bondMarketCode", true);
            querySorts.add(sort);
        } else {
            querySorts.addAll(req.getSorts());
        }
        if (querySorts.size() != 1) {
            LogTool.warn("可转债持仓明细排序参数不为1！");
        }

        if (Objects.equals(yyyyMMdd, bizDate) && nowIsBefore930()) {
            bizDate = getLastBizDateOfXSHG(bizDate);
            LogTool.info("可转债持仓早于9点半，查询昨日数据！");
        }
        List<ConvertibleBondPositionDetailDO> retDOS;
        if (Objects.equals(yyyyMMdd, bizDate)) {
            retDOS = new ArrayList<>();
            //查询日不是交易日
            try {
                if (!ibTradeDateService.isTradeDateWithMarketCode(bizDate, TradeMarketCodeEnum.XSHG)) {
                    LogTool.info("查询日不是交易日，持仓数据为空");
                    return response.setData(Collections.emptyList()).setOk();
                }
            } catch (Exception e) {
                LogTool.error("可转债损益-获取交易日失败", e);
            }
            List<StockInfoVO> stockInfoVOS = RedisUtil.getForValue(CommonConstant.PREFIX_CONVERTIBLE_BOND_AMOUNT, new TypeToken<List<StockInfoVO>>() {
            });
            if (ObjectUtil.isNull(stockInfoVOS)) {
                LogTool.warn("可转债损益报表-redis中O32持仓数据为null");
                stockInfoVOS = selectConvertibleBondAmount();
                RedisUtil.opsForValue().set(CommonConstant.PREFIX_CONVERTIBLE_BOND_AMOUNT, JsonUtil.toJson(stockInfoVOS), CommonConstant.O32_CACHE_TIME, TimeUnit.HOURS);
            }
            if (CollUtil.isEmpty(stockInfoVOS)) {
                LogTool.warn("可转债损益报表-查询O32数据库 持仓数据为空");
                return response.setData(Collections.emptyList()).setOk();
            }
            // 根据bondMarketCode过滤
            if (StrUtil.isNotBlank(bondMarketCode)) {
                stockInfoVOS = stockInfoVOS.stream()
                        .filter(v -> (StrUtil.isNotBlank(v.getVcInterCode()) && ConvertUtil.tryParseConvertibleBondSuffix(v.getVcInterCode()).equals(bondMarketCode)))
                        .collect(Collectors.toList());
            }
            // 根据fundId过滤
            if (ObjectUtil.isNotNull(fundId)) {
                stockInfoVOS = stockInfoVOS.stream()
                        .filter(v -> (ObjectUtil.isNotNull(v.getLFundId()) && v.getLFundId().equals(fundId)))
                        .collect(Collectors.toList());
            }

            //获取可转债行情
            List<String> codeList = stockInfoVOS.stream()
                    .map(v -> ConvertUtil.tryParseConvertibleBondSuffix(v.getVcInterCode()))
                    .filter(StrUtil::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());
            List<ConvertibleBondMarketDataVO> convertibleBondMarketDatas = getConvertibleBondMarketData(codeList);
            Map<String, ConvertibleBondMarketDataVO> marketDataVOMap = CollUtil.emptyIfNull(convertibleBondMarketDatas).stream()
                    .filter(v -> ObjectUtil.isNotNull(v) && StrUtil.isNotBlank(v.getCode()))
                    .collect(Collectors.toMap(ConvertibleBondMarketDataVO::getCode, Function.identity(), (o1, o2) -> o1));
            stockInfoVOS.forEach(v -> {
                ConvertibleBondPositionDetailDO detailDO = new ConvertibleBondPositionDetailDO();
                try {
                    detailDO.setBondMarketCode(ConvertUtil.tryParseConvertibleBondSuffix(v.getVcInterCode()));
                    detailDO.setBondName(v.getVcStockName());
                    detailDO.setFundId(v.getLFundId());
                    detailDO.setStockType(v.getCStockType());
                    detailDO.setCurrentAmount(v.getLCurrentAmount());
                    //获取昨日持仓量
                    detailDO.setYesterdayAmount(getYesterdayAmount(ConvertUtil.tryParseConvertibleBondSuffix(v.getVcInterCode()), v.getLDate()));
                    detailDO.setBuyAmount(v.getLBuyAmount());
                    detailDO.setSaleAmount(v.getLSaleAmount());
                    ConvertibleBondMarketDataVO marketDataVO = marketDataVOMap.get(Opt.ofNullable(detailDO.getBondMarketCode()).orElse(""));
                    //行情字段都可能为 null
                    detailDO.setPreClose(Opt.ofNullable(marketDataVO).map(mv -> ObjectUtil.isNotNull(mv.getPreClose()) ? BigDecimal.valueOf(mv.getPreClose()).setScale(4, RoundingMode.HALF_UP) : null)
                            .orElse(null));
                    detailDO.setClose(Opt.ofNullable(marketDataVO).map(mv -> ObjectUtil.isNotNull(mv.getClose()) ? BigDecimal.valueOf(mv.getClose()).setScale(4, RoundingMode.HALF_UP) : null)
                            .orElse(null));
                    detailDO.setRatio(Opt.ofNullable(marketDataVO).map(mv -> ObjectUtil.isNotNull(mv.getRatio()) ? BigDecimal.valueOf(mv.getRatio()).setScale(2, RoundingMode.HALF_UP) : null)
                            .orElse(null));
                    //计算当前市值
                    detailDO.setCurrentMarketValue(calCurrentMarketValue(detailDO));
                    //计算实时盈亏
                    detailDO.setCurrentProfitAndLoss(calCurrentProfitAndLoss(detailDO, v));
                    //计算成本价（净价）
                    detailDO.setCostPrice(calCostPrice(detailDO, v));
                    //计算盈亏率（%）
                    detailDO.setProfitAndLossRatio(calProfitAndLossRatio(detailDO, v));
                    validPosition(detailDO);
                } catch (Exception e) {
                    LogTool.warn("可转债损益报表-行情计算出错detailDO:{},{}", detailDO, e);
                }
                retDOS.add(detailDO);
            });
            //设置债券评级
            List<String> bondMarketCodes = retDOS.stream()
                    .filter(v -> stock_type_bond.equals(v.getStockType()) && StrUtil.isNotBlank(v.getBondMarketCode()))
                    .map(ConvertibleBondPositionDetailDO::getBondMarketCode)
                    .collect(Collectors.toList());
            Map<String, String> bondAndBDRatingMap = getBDRating(bondMarketCodes);
            retDOS.forEach(v -> {
                if(stock_type_bond.equals(v.getStockType())) {
                    String BDRating = bondAndBDRatingMap.get(v.getBondMarketCode());
                    v.setBDRating(ObjectUtil.isNotNull(BDRatingEnum.getEnumByCode(BDRating)) ? BDRating : BDRatingEnum.OTHER.getCode());
                }
            });
        } else {
            //查询历史持仓明细
            retDOS = ConvertibleBondPositionDetailDao.Instance.queryHisRecords(bizDate, bondMarketCode, fundId);
            if (CollUtil.isEmpty(retDOS)) {
                return response.setData(Collections.emptyList()).setOk();
            }
        }
        //排序
        compareListByQuerySorts(querySorts, retDOS);
        ApiResponse<ConvertibleBondPositionDetailDO> apiResponse = response.setData(ApiUtil.getPageRecord(retDOS, pageNo, pageSize)).setCount(retDOS.size()).setOk();
        //将汇总行放到最后
        if (addTotal) {
            ConvertibleBondPositionDetailDO totalDO = calPositionTotal(retDOS);
            apiResponse.getData().add(totalDO);
        }
        return apiResponse;
    }


    private Map<String, String> getBDRating(List<String> bondMarketCodes) {
        if (CollUtil.isEmpty(bondMarketCodes)) {
            return Collections.emptyMap();
        }
        Map<String, String> bondAndBDRatingMap = new HashMap<>();
        List<String> SHCodes = bondMarketCodes.stream().filter(v -> StrUtil.contains(v, ".SH")).collect(Collectors.toList());
        List<String> SZCodes = bondMarketCodes.stream().filter(v -> StrUtil.contains(v, ".SZ")).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(SHCodes)) {
            List<BondValuationParam> bondValuationParamList = SHCodes.stream()
                    .map(item -> new BondValuationParam(item, TimeTool.CURRENT.getCurrentDate(), CHOICE_VALUE_TYPE, CHOICE_CLEAR_TYPE, CHOICE_BOND_PRICE))
                    .collect(Collectors.toList());
            List<BondValuationRespDTO> bondValuationRespDTOS = bondChoiceService.queryBondValuationFromChoice(bondValuationParamList, HtMarketEnum.XSHG);
            if(!Objects.equals(bondValuationRespDTOS.size(), SHCodes.size())){
                LogTool.info("可转债损益报表-choice返回的评级与请求的代码不匹配,重试一次，bondMarketCodes:{},bondValuationRespDTOS:{}", SHCodes,bondValuationRespDTOS);
                bondValuationRespDTOS = bondChoiceService.queryBondValuationFromChoice(bondValuationParamList, HtMarketEnum.XSHG);
                if(!Objects.equals(bondValuationRespDTOS.size(), SHCodes.size())){
                    LogTool.error("可转债损益报表-choice返回的评级与请求的代码不匹配,bondMarketCodes:{},bondValuationRespDTOS:{}", SHCodes,bondValuationRespDTOS);
                }
            }
            Map<String, String> bondValuationRespDTOMap = bondValuationRespDTOS.stream()
                    .filter(v -> StrUtil.isNotBlank(v.getSecuCode()) && StrUtil.isNotBlank(v.getBdRating()))
                    .collect(Collectors.toMap(BondValuationRespDTO::getSecuCode, BondValuationRespDTO::getBdRating, (oldKey, newKey) -> newKey));
            bondAndBDRatingMap.putAll(bondValuationRespDTOMap);
        }

        if (CollUtil.isNotEmpty(SZCodes)) {
            List<BondValuationParam> bondValuationParamList = SZCodes.stream()
                    .map(item -> new BondValuationParam(item, TimeTool.CURRENT.getCurrentDate(), CHOICE_VALUE_TYPE, CHOICE_CLEAR_TYPE, CHOICE_BOND_PRICE))
                    .collect(Collectors.toList());
            List<BondValuationRespDTO> bondValuationRespDTOS = bondChoiceService.queryBondValuationFromChoice(bondValuationParamList, HtMarketEnum.XSHE);
            if(!Objects.equals(bondValuationRespDTOS.size(), SZCodes.size())){
                LogTool.info("可转债损益报表-choice返回的评级与请求的代码不匹配,重试一次，bondMarketCodes:{},bondValuationRespDTOS:{}", SZCodes,bondValuationRespDTOS);
                bondValuationRespDTOS = bondChoiceService.queryBondValuationFromChoice(bondValuationParamList, HtMarketEnum.XSHE);
                if(!Objects.equals(bondValuationRespDTOS.size(), SZCodes.size())){
                    LogTool.error("可转债损益报表-choice返回的评级与请求的代码不匹配,bondMarketCodes:{},bondValuationRespDTOS:{}", SZCodes,bondValuationRespDTOS);
                }
            }
            Map<String, String> bondValuationRespDTOMap = bondValuationRespDTOS.stream()
                    .filter(v -> StrUtil.isNotBlank(v.getSecuCode()) && StrUtil.isNotBlank(v.getBdRating()))
                    .collect(Collectors.toMap(BondValuationRespDTO::getSecuCode, BondValuationRespDTO::getBdRating, (oldKey, newKey) -> newKey));
            bondAndBDRatingMap.putAll(bondValuationRespDTOMap);
        }

        return bondAndBDRatingMap;
    }


    private void validPosition(ConvertibleBondPositionDetailDO detailDO) {
        BigDecimal delta1 = BigDecimalUtil.subtract(detailDO.getCurrentAmount(), detailDO.getYesterdayAmount());
        BigDecimal delta2 = BigDecimalUtil.subtract(detailDO.getBuyAmount(), detailDO.getSaleAmount());
        if (delta1.compareTo(delta2) != 0) {
            LogTool.info("【可转债】请检查可转债持仓数量:{}", detailDO);
        }
    }


    private void compareListByQuerySorts(List<EmSort> querySorts, List<ConvertibleBondPositionDetailDO> retVOS) {
        if (CollUtil.isEmpty(querySorts)) {
            return;
        }
        for (EmSort sortingField : querySorts) {
            String field = sortingField.getPropertyName();
            boolean isAsc = sortingField.isAscending();
            if (SORT_FIELD_FUNCTION.containsKey(field)) {
                if (isAsc) {
                    retVOS.sort(ASC_SORT_MAP.get(field));
                } else {
                    retVOS.sort(DESC_SORT_MAP.get(field));
                }
            }
        }
    }

    public List<ConvertibleBondPositionDetailDO> getLastBizDatePositionDB(Integer lastBizDateOfXSHG) {
        try {
            List<ConvertibleBondPositionDetailDO> detailDOSOfLastBizDate = LAST_BIZDATE_POSITION_DB_MAP.get(lastBizDateOfXSHG);
            if (ObjectUtil.isNotNull(detailDOSOfLastBizDate)) {
                return detailDOSOfLastBizDate;
            }
            List<ConvertibleBondPositionDetailDO> lastBizDatePosition = ConvertibleBondPositionDetailDao.Instance.queryHisRecords(lastBizDateOfXSHG, null,null);
            LAST_BIZDATE_POSITION_DB_MAP.putIfAbsent(lastBizDateOfXSHG, lastBizDatePosition);
            return lastBizDatePosition;
        } catch (Exception e) {
            LogTool.error("getLastBizDatePositionDB error,", e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取昨日持仓量
     */
    private BigDecimal getYesterdayAmount(String codeWithSuffix, Integer bizDate) {
        try {
            Integer lastBizDateOfXSHG = getLastBizDateOfXSHG(bizDate);
            if (Objects.isNull(lastBizDateOfXSHG) || StrUtil.isBlank(codeWithSuffix)) {
                return null;
            }
            String lastDateAndCode = lastBizDateOfXSHG + "_" + codeWithSuffix;
            BigDecimal lastTradeDateAmount = YESTERDAY_AMOUNT.get(lastDateAndCode);
            if (ObjectUtil.isNotNull(lastTradeDateAmount)) {
                return lastTradeDateAmount;
            }
            AtomicReference<BigDecimal> ret = new AtomicReference<>();
            //从归档表中获取昨日持仓量（前一交易日收盘时点的[最新持仓量]）
            List<ConvertibleBondPositionDetailDO> lastBizDatePosition = getLastBizDatePositionDB(lastBizDateOfXSHG);
            lastBizDatePosition.forEach(position -> {
                if (ObjectUtil.isNotNull(position.getCurrentAmount())) {
                    YESTERDAY_AMOUNT.putIfAbsent(lastBizDateOfXSHG + "_" + position.getBondMarketCode(), position.getCurrentAmount());
                    if (codeWithSuffix.equals(position.getBondMarketCode())) {
                        ret.set(position.getCurrentAmount());
                    }
                }
            });
            if (ObjectUtil.isNotNull(ret.get())) {
                LogTool.info("从归档表中获取昨日持仓，codeWithSuffix：{}，lastBizDateOfXSHG：{}", codeWithSuffix, lastBizDateOfXSHG);
                return ret.get();
            }
            //从redis获取  昨日O32历史表中获取持仓
            Integer yyyyMMdd = Integer.parseInt(TimeTool.CURRENT.getCunrrentyyyyMMdd());
            Integer yesterdayOfXSHG = getLastBizDateOfXSHG(yyyyMMdd);
            if (Objects.equals(yyyyMMdd, bizDate)) {
                List<StockInfoVO> stockInfoVOS = RedisUtil.getForValue(CommonConstant.PREFIX_YESTERDAY_CONVERTIBLE_BOND_AMOUNT, new TypeToken<List<StockInfoVO>>() {
                });

                CollUtil.emptyIfNull(stockInfoVOS).forEach(position -> {
                    if (ObjectUtil.isNotNull(position.getLCurrentAmount())) {
                        String bondMarketCode = ConvertUtil.tryParseConvertibleBondSuffix(position.getVcInterCode());
                        YESTERDAY_AMOUNT.putIfAbsent(position.getLDate() + "_" + bondMarketCode, position.getLCurrentAmount());
                        if (codeWithSuffix.equals(bondMarketCode) && yesterdayOfXSHG.equals(position.getLDate())) {
                            ret.set(position.getLCurrentAmount());
                        }
                    }
                });
            }
            if (ObjectUtil.isNotNull(ret.get())) {
                LogTool.info("从O32历史表中获取昨日持仓，codeWithSuffix：{}，yesterdayOfXSHG：{}", codeWithSuffix, yesterdayOfXSHG);
                return ret.get();
            }
            LogTool.info("归档表和O32未获取到昨日持仓，以0计，codeWithSuffix：{}，lastBizDateOfXSHG：{}", codeWithSuffix, lastBizDateOfXSHG);
            return BigDecimal.ZERO;
        } catch (Exception e) {
            LogTool.error("获取前一日持仓量失败，codeWithSuffix：{}，bizDate：{}", codeWithSuffix, bizDate, e);
            return null;
        }
    }

    public BigDecimal getMarketValueOfLastBizDate(Integer lastBizDateOfXSHG) {
        if (!O32Enable) {
            return null;
        }
        try {
            //先从明细表累计获取昨日市值，避免精度问题
            ConvertibleBondPositionDetailREQ detailREQ = new ConvertibleBondPositionDetailREQ();
            detailREQ.setBizDate(lastBizDateOfXSHG);
            detailREQ.setPageNo(1);
            detailREQ.setPageSize(9999999);
            ApiResponse<ConvertibleBondPositionDetailDO> apiResponse = selectConvertibleBondPositionDetailDO(detailREQ, true);
            List<ConvertibleBondPositionDetailDO> detailDOS = apiResponse.getData();
            if (CollUtil.isNotEmpty(detailDOS)) {
                ConvertibleBondPositionDetailDO totalDetail = detailDOS.get(detailDOS.size() - 1);
                BigDecimal detailDOCurrentMarketValue = BigDecimalUtil.divide(totalDetail.getCurrentMarketValue(), BigDecimal.valueOf(CommonConstant.A_HUNDRED_MILLION)).setScale(8, RoundingMode.HALF_UP);
                if (ObjectUtil.isNotNull(detailDOCurrentMarketValue)) {
                    YESTERDAY_MARKET_VALUE.set(detailDOCurrentMarketValue);
                    LogTool.info("可转债，明细表，缓存昨日总市值，lastBizDateOfXSHG：{}，yesterdayMarketValue：{}", lastBizDateOfXSHG, detailDOCurrentMarketValue);
                    return detailDOCurrentMarketValue;
                } else {
                    LogTool.error("可转债，明细表 getMarketValueOfLastBizDate 为空，lastBizDateOfXSHG：{}", lastBizDateOfXSHG);
                }
            }
        } catch (Exception e) {
            LogTool.error("可转债，明细表 getMarketValueOfLastBizDate 失败，lastBizDateOfXSHG：{}", lastBizDateOfXSHG, e);
        }

        try {
            //如果没拿到再从概览表中获取
            LogTool.info("从概览表获取昨日市值", lastBizDateOfXSHG);
            ConvertibleBondPositionOverviewDO overviewDOOfLastBizDate = ConvertibleBondPositionOverviewDao.Instance.queryHisRecord(lastBizDateOfXSHG);
            BigDecimal bigDecimal = Opt.ofNullable(overviewDOOfLastBizDate).map(ConvertibleBondPositionOverviewDO::getCurrentMarketValue).get();
            if (ObjectUtil.isNotNull(bigDecimal)) {
                YESTERDAY_MARKET_VALUE.set(bigDecimal);
                LogTool.info("缓存昨日总市值，概览表，lastBizDateOfXSHG：{}，yesterdayMarketValue：{}", lastBizDateOfXSHG, bigDecimal);
                return bigDecimal;
            } else {
                LogTool.error("可转债，概览表，getMarketValueOfLastBizDate失败，lastBizDateOfXSHG：{}", lastBizDateOfXSHG);
            }
        } catch (Exception e) {
            LogTool.error("可转债，概览表，error getMarketValueOfLastBizDate，lastBizDateOfXSHG：{}", lastBizDateOfXSHG, e);
        }
        return null;
    }

    public Integer getLastBizDateOfXSHG(Integer bizDate) {
        if (ObjectUtil.isNull(bizDate)) {
            LogTool.error("获取交易所前一交易日入参为空");
            return null;
        }
        Integer lastTradeDate = MARKET_LAST_BIZ_DATE.get(bizDate);
        if (ObjectUtil.isNotNull(lastTradeDate)) {
            return lastTradeDate;
        }
        try {
            String lastTradeDateString = ibTradeDateService.getLastTradeDateWithMarketCode(bizDate, TradeMarketCodeEnum.XSHG);
            if (CharSequenceUtil.isNotBlank(lastTradeDateString)) {
                lastTradeDate = Integer.valueOf(lastTradeDateString);
                MARKET_LAST_BIZ_DATE.put(bizDate, lastTradeDate);
                return lastTradeDate;
            }
        } catch (Exception e) {
            LogTool.error("可转债获取前一交易日出错", e);
        }
        return null;
    }

    public void clearCache() {
        MARKET_LAST_BIZ_DATE.clear();
        LAST_BIZDATE_POSITION_DB_MAP.clear();
        YESTERDAY_AMOUNT.clear();
        YESTERDAY_MARKET_VALUE.set(null);
    }

    /**
     * 计算汇总
     *
     * @param detailDOS
     */
    private ConvertibleBondPositionDetailDO calPositionTotal(List<ConvertibleBondPositionDetailDO> detailDOS) {
        ConvertibleBondPositionDetailDO totalDO = new ConvertibleBondPositionDetailDO();
        try {
            List<ConvertibleBondPositionDetailVO> vos = new ArrayList<>();
            CollUtil.emptyIfNull(detailDOS).forEach(detailDO -> {
                ConvertibleBondPositionDetailVO convertibleBondPositionDetailVO = ConvertibleBondPositionDetailVO.fromDetailDO(detailDO);
                vos.add(convertibleBondPositionDetailVO);
            });
            totalDO.setBizDate(Integer.parseInt(TimeTool.CURRENT.getCunrrentyyyyMMdd()));
            totalDO.setDel(EnumDel.USE.getValue());
            //最新市值 亿元
            AtomicReference<BigDecimal> totalCurrentMarketValue = new AtomicReference<>(BigDecimal.ZERO);
            //昨日持仓量汇总
            AtomicReference<BigDecimal> totalYesterdayAmount = new AtomicReference<>(BigDecimal.ZERO);
            //当日买量汇总
            AtomicReference<BigDecimal> totalBuyAmount = new AtomicReference<>(BigDecimal.ZERO);
            //当日卖量汇总
            AtomicReference<BigDecimal> totalSaleAmount = new AtomicReference<>(BigDecimal.ZERO);
            //当日实时盈亏 万元
            AtomicReference<BigDecimal> totalCurrentProfitAndLoss = new AtomicReference<>(BigDecimal.ZERO);

            CollUtil.emptyIfNull(vos).forEach(v -> {
                if (ObjectUtil.isNull(v)) {
                    return;
                }
                totalCurrentMarketValue.set(BigDecimalUtil.add(totalCurrentMarketValue.get(), v.getCurrentMarketValue()));
                totalYesterdayAmount.set(BigDecimalUtil.add(totalYesterdayAmount.get(), v.getYesterdayAmount()));
                totalBuyAmount.set(BigDecimalUtil.add(totalBuyAmount.get(), v.getBuyAmount()));
                totalSaleAmount.set(BigDecimalUtil.add(totalSaleAmount.get(), v.getSaleAmount()));
                totalCurrentProfitAndLoss.set(BigDecimalUtil.add(totalCurrentProfitAndLoss.get(), v.getCurrentProfitAndLoss()));
            });

            totalDO.setCurrentMarketValue(totalCurrentMarketValue.get().setScale(0, RoundingMode.HALF_UP));
            totalDO.setYesterdayAmount(totalYesterdayAmount.get().setScale(0, RoundingMode.HALF_UP));
            totalDO.setBuyAmount(totalBuyAmount.get().setScale(0, RoundingMode.HALF_UP));
            totalDO.setSaleAmount(totalSaleAmount.get().setScale(0, RoundingMode.HALF_UP));
            totalDO.setCurrentProfitAndLoss(totalCurrentProfitAndLoss.get().setScale(0, RoundingMode.HALF_UP));
        } catch (Exception e) {
            LogTool.error("【可转债】 calPositionTotal 计算出错", e);
        }
        return totalDO;
    }

    /**
     * 计算实时损益
     *
     * @param detailDOS
     */
    private ConvertibleBondPositionOverviewDO calPositionOverview(List<ConvertibleBondPositionDetailDO> detailDOS) {
        ConvertibleBondPositionOverviewDO overviewDO = new ConvertibleBondPositionOverviewDO();
        try {
            List<ConvertibleBondPositionDetailVO> vos = new ArrayList<>();
            CollUtil.emptyIfNull(detailDOS).forEach(detailDO -> {
                ConvertibleBondPositionDetailVO convertibleBondPositionDetailVO = ConvertibleBondPositionDetailVO.fromDetailDO(detailDO);
                vos.add(convertibleBondPositionDetailVO);
            });
            overviewDO.setBizDate(Integer.parseInt(TimeTool.CURRENT.getCunrrentyyyyMMdd()));
            overviewDO.setDel(EnumDel.USE.getValue());
            //最新市值 亿元
            AtomicReference<BigDecimal> totalCurrentMarketValue = new AtomicReference<>(BigDecimal.ZERO);
            //ETF市值(亿)
            AtomicReference<BigDecimal> totalEtfMarketValue = new AtomicReference<>(BigDecimal.ZERO);
            //债券市值(亿)
            AtomicReference<BigDecimal> totalBondMarketValue = new AtomicReference<>(BigDecimal.ZERO);

            //当日实时盈亏 万元
            AtomicReference<BigDecimal> totalCurrentProfitAndLoss = new AtomicReference<>(BigDecimal.ZERO);
            //当前持仓只数
            overviewDO.setBondNum(CollUtil.isNotEmpty(detailDOS) ? detailDOS.size() : 0);
            //当前持仓只数（涨）
            AtomicInteger riseCount = new AtomicInteger();
            //当前持仓只数（平）
            AtomicInteger flatCount = new AtomicInteger();
            //当前持仓只数（跌）
            AtomicInteger fallCount = new AtomicInteger();
            CollUtil.emptyIfNull(vos).forEach(v -> {
                if (ObjectUtil.isNull(v)) {
                    return;
                }
                totalCurrentMarketValue.set(BigDecimalUtil.add(totalCurrentMarketValue.get(), v.getCurrentMarketValue()));
                if(stock_type_etf.equals(v.getStockType())){
                    totalEtfMarketValue.set(BigDecimalUtil.add(totalEtfMarketValue.get(), v.getCurrentMarketValue()));
                }else if (stock_type_bond.equals(v.getStockType())){
                    totalBondMarketValue.set(BigDecimalUtil.add(totalBondMarketValue.get(), v.getCurrentMarketValue()));
                }
                totalCurrentProfitAndLoss.set(BigDecimalUtil.add(totalCurrentProfitAndLoss.get(), v.getCurrentProfitAndLoss()));
                if (ObjectUtil.isNotNull(v.getRatio())) {
                    if (BigDecimal.ZERO.compareTo(v.getRatio()) < 0) {
                        riseCount.getAndIncrement();
                    } else if (BigDecimal.ZERO.compareTo(v.getRatio()) == 0) {
                        flatCount.getAndIncrement();
                    } else if (BigDecimal.ZERO.compareTo(v.getRatio()) > 0) {
                        fallCount.getAndIncrement();
                    }
                }
            });
            BigDecimal yesterdayMarketValueBigDecimal = YESTERDAY_MARKET_VALUE.get();
            if (ObjectUtil.isNull(yesterdayMarketValueBigDecimal)) {
                Integer yyyyMMdd = Integer.parseInt(TimeTool.CURRENT.getCunrrentyyyyMMdd());
                Integer lastBizDateOfXSHG = getLastBizDateOfXSHG(yyyyMMdd);
                yesterdayMarketValueBigDecimal = getMarketValueOfLastBizDate(lastBizDateOfXSHG);
            }
            if (O32Enable && ObjectUtil.isNull(yesterdayMarketValueBigDecimal)) {
                LogTool.error("calPositionOverview yesterdayMarketValueBigDecimal is null");
            }
            BigDecimal changeMarketValue = BigDecimalUtil.subtract(totalCurrentMarketValue.get(), BigDecimalUtil.multiply(yesterdayMarketValueBigDecimal, BigDecimal.valueOf(CommonConstant.A_HUNDRED_MILLION)));
            overviewDO.setCurrentMarketValue(BigDecimalUtil.divide(totalCurrentMarketValue.get(), BigDecimal.valueOf(CommonConstant.A_HUNDRED_MILLION)).setScale(8, RoundingMode.HALF_UP));
            overviewDO.setEtfMarketValue(BigDecimalUtil.divide(totalEtfMarketValue.get(), BigDecimal.valueOf(CommonConstant.A_HUNDRED_MILLION)).setScale(8, RoundingMode.HALF_UP));
            overviewDO.setBondMarketValue(BigDecimalUtil.divide(totalBondMarketValue.get(), BigDecimal.valueOf(CommonConstant.A_HUNDRED_MILLION)).setScale(8, RoundingMode.HALF_UP));

            //当日规模变动（亿）
            overviewDO.setChangeMarketValue(BigDecimalUtil.divide(changeMarketValue, BigDecimal.valueOf(CommonConstant.A_HUNDRED_MILLION)).setScale(8, RoundingMode.HALF_UP));
            overviewDO.setCurrentProfitAndLoss(BigDecimalUtil.divide(totalCurrentProfitAndLoss.get(), BigDecimal.valueOf(CommonConstant.TEN_THOUSAND)).setScale(8, RoundingMode.HALF_UP));
            LogTool.info("【可转债实时损益】汇总(精确)：最新市值{}，eft市值{}，债券市值{}，当日规模变动：{}，当日实时盈亏:{}",
                    totalCurrentMarketValue.get(), totalEtfMarketValue.get(), totalBondMarketValue.get(),changeMarketValue, totalCurrentProfitAndLoss.get());
            overviewDO.setRiseBondNum(riseCount.get());
            overviewDO.setFlatBondNum(flatCount.get());
            overviewDO.setFallBondNum(fallCount.get());
            //中证转债实时涨跌幅
            List<ConvertibleBondMarketDataVO> CSIConvertibleBondMD = getConvertibleBondMarketData(CSI_CONVERTIBLE_BOND_List);
            if (CSIConvertibleBondMD.size() == 1 && ObjectUtil.isNotNull(CSIConvertibleBondMD.get(0))) {
                overviewDO.setRatio(Opt.ofNullable(CSIConvertibleBondMD.get(0).getRatio()).map(ratio -> BigDecimal.valueOf(ratio).setScale(2, RoundingMode.HALF_UP)).orElse(null));
            } else {
                LogTool.warn("获取中证转债实时涨跌幅失败，CSIConvertibleBondMD：{}", CSIConvertibleBondMD);
            }
        } catch (Exception e) {
            LogTool.error("【可转债实时损益】计算出错", e);
        }
        return overviewDO;
    }


    private BigDecimal calCurrentMarketValue(ConvertibleBondPositionDetailDO detailDO) {
        if (Objects.isNull(detailDO.getCurrentAmount())) {
            LogTool.warn("可转债损益报表-计算当前市值出错:{}", detailDO);
            return null;
        }
        if (ObjectUtil.isNotNull(detailDO.getClose())) {
            return BigDecimalUtil.multiply(detailDO.getCurrentAmount(), detailDO.getClose()).setScale(0, RoundingMode.HALF_UP);
        } else if (ObjectUtil.isNotNull(detailDO.getPreClose())) {
            //如果没有最新价，用前一日收盘价计算最新市值
            return BigDecimalUtil.multiply(detailDO.getCurrentAmount(), detailDO.getPreClose()).setScale(0, RoundingMode.HALF_UP);
        }
        LogTool.warn("可转债损益报表-计算当前市值出错:{}", detailDO);
        return null;
    }

    private BigDecimal calCurrentProfitAndLoss(ConvertibleBondPositionDetailDO detailDO, StockInfoVO stockInfoVO) {
        if (Objects.isNull(detailDO.getCurrentMarketValue()) || Objects.isNull(detailDO.getYesterdayAmount())
                || Objects.isNull(detailDO.getPreClose()) || Objects.isNull(stockInfoVO.getEnAbsBuyBalance())
                || Objects.isNull(stockInfoVO.getEnBondInterest()) || Objects.isNull(stockInfoVO.getLBuyAmount())
                || Objects.isNull(stockInfoVO.getLSaleAmount())
        ) {
            LogTool.warn("可转债损益报表-计算实时盈亏出错:detailDO:{},stockInfoVO:{}", detailDO, stockInfoVO);
            return null;
        }
        //实时盈亏=最新市值-昨日持仓量*昨收盘-净买入金额-百元债券利息*(当日买量-当日卖量)
        BigDecimal mvSubYesMv = BigDecimalUtil.subtract(detailDO.getCurrentMarketValue(), BigDecimalUtil.multiply(detailDO.getYesterdayAmount(), detailDO.getPreClose()));
        return BigDecimalUtil.subtract(BigDecimalUtil.subtract(mvSubYesMv, stockInfoVO.getEnAbsBuyBalance()),
                BigDecimalUtil.multiply(stockInfoVO.getEnBondInterest(), BigDecimalUtil.subtract(stockInfoVO.getLBuyAmount(), stockInfoVO.getLSaleAmount())));
    }

    private BigDecimal calCostPrice(ConvertibleBondPositionDetailDO detailDO, StockInfoVO v) {
        //如果最新持仓量为0，costPrice=0
        if (BigDecimal.ZERO.compareTo(v.getLCurrentAmount()) == 0) {
            return BigDecimal.ZERO;
        }
        //如果最新持仓量不为0,成本价（净价）=当前成本/最新持仓量，当前成本=前一日当前成本+当日买入总金额 - (前一日当前成本+当日买入总金额+当日买入总费用)/(当前数量+当日卖量)*当日卖量
        //(当前数量+当日卖量)=(前一日期末数量+当日买量)
        if (Objects.isNull(v.getEnUntransferedInvest()) || Objects.isNull(v.getEnBuyBalance())
                || Objects.isNull(v.getLCurrentAmount()) || Objects.isNull(v.getLSaleAmount())
                || Objects.isNull(v.getEnBuyFee())) {
            LogTool.warn("可转债损益报表-计算成本价出错:detailDO:{},stockInfoVO:{}", detailDO, v);
            return null;
        }
        BigDecimal saleInvest = BigDecimalUtil.multiply(v.getLSaleAmount(), BigDecimalUtil.divide(BigDecimalUtil.add(v.getEnUntransferedInvest(), BigDecimalUtil.add(v.getEnBuyBalance(), v.getEnBuyFee())),
                BigDecimalUtil.add(v.getLCurrentAmount(), v.getLSaleAmount())));
        BigDecimal currentInvest = BigDecimalUtil.subtract(BigDecimalUtil.add(v.getEnUntransferedInvest(), v.getEnBuyBalance()), saleInvest);
        //保留4位小数
        return BigDecimalUtil.divide(currentInvest, detailDO.getCurrentAmount(), 4);
    }

    private BigDecimal calProfitAndLossRatio(ConvertibleBondPositionDetailDO detailDO, StockInfoVO v) {
        //如果最新持仓量为0，profitAndLossRatio=0
        if (BigDecimal.ZERO.compareTo(v.getLCurrentAmount()) == 0) {
            return BigDecimal.ZERO;
        }
        // 如果最新持仓量不为0， 盈亏率（%）=(最新价-每百元利息)/成本价-1 ,乘以100，保留2位小数
        if (Objects.isNull(detailDO.getClose()) || Objects.isNull(v.getEnBondInterest()) || Objects.isNull(detailDO.getCostPrice())) {
            LogTool.warn("可转债损益报表-计算盈亏率出错:detailDO:{},stockInfoVO:{}", detailDO, v);
            return null;
        }
        BigDecimal profitAndLossPrice = BigDecimalUtil.subtract(BigDecimalUtil.divide(BigDecimalUtil.subtract(detailDO.getClose(), v.getEnBondInterest()), detailDO.getCostPrice()), BigDecimal.ONE);
        return BigDecimalUtil.multiply(profitAndLossPrice, BigDecimal.valueOf(100), 2);
    }


    /**
     * @param codes 债券代码带后缀集合
     * @return
     */
    private List<ConvertibleBondMarketDataVO> getConvertibleBondMarketData(List<String> codes) {
        if (CollUtil.isEmpty(codes)) {
            return Collections.emptyList();
        }
        List<ConvertibleBondMarketDataVO> ret = new CopyOnWriteArrayList<>();

        List<List<String>> lists = CollUtil.split(codes, 100);
        lists.parallelStream().forEach(splitCodes -> {
            String buildUrl = UrlBuilder.create()
                    .setScheme("http")
                    .setHost(PmmsServiceProperties.getIp())
                    .setPort(PmmsServiceProperties.getHttpPort())
                    .addPath(PmmsServiceProperties.getQuoteSnapshotPath())
                    .addQuery("codes", StrUtil.join(",", splitCodes))
                    .build();
            try {
                String result = HttpUtil.get(buildUrl);
                ApiResponse<ConvertibleBondMarketDataVO> responseDTO = GsonUtil.fromJson(
                        result, new TypeToken<ApiResponse<ConvertibleBondMarketDataVO>>() {
                        });
                if (ObjectUtil.isNull(responseDTO) || ObjectUtil.isEmpty(responseDTO.getData())) {
                    LogTool.error("【可转债行情】获取失败，responseDTO返回:{},url：{}", responseDTO, buildUrl);
                    return;
                }
                LogTool.info("【可转债行情】获取成功，返回:{},url:{}", responseDTO.getData(), buildUrl);
                ret.addAll(responseDTO.getData());
            } catch (Exception e) {
                LogTool.error("【可转债行情】获取失败,url:{}", buildUrl, e);
            }
        });
        return ret;
    }

    public ApiResponse<ConvertibleBondPositionOverviewVO> selectConvertibleBondPositionOverview(ConvertibleBondPositionDetailREQ req) {
        Integer yyyyMMdd = Integer.parseInt(TimeTool.CURRENT.getCunrrentyyyyMMdd());
        Integer bizDate = req.getBizDate();
        if (Objects.equals(yyyyMMdd, bizDate) && nowIsBefore930()) {
            bizDate = getLastBizDateOfXSHG(bizDate);
            LogTool.info("可转债实时损益早于9点半，查询昨日数据！bizDate={}", bizDate);
        }
        ConvertibleBondPositionOverviewDO convertibleBondPositionOverviewDO;
        if (Objects.equals(yyyyMMdd, bizDate)) {
            ConvertibleBondPositionDetailREQ detailREQ = new ConvertibleBondPositionDetailREQ();
            detailREQ.setBizDate(Integer.parseInt(TimeTool.CURRENT.getCunrrentyyyyMMdd()));
            detailREQ.setPageNo(1);
            detailREQ.setPageSize(9999999);
            ApiResponse<ConvertibleBondPositionDetailDO> apiResponse = selectConvertibleBondPositionDetailDO(detailREQ, false);
            List<ConvertibleBondPositionDetailDO> detailDOS = apiResponse.getData();
            detailDOS.forEach(vo -> {
                vo.setBizDate(Integer.parseInt(TimeTool.CURRENT.getCunrrentyyyyMMdd()));
                vo.setEID(RandomUtil.randomUUID());
            });
            convertibleBondPositionOverviewDO = calPositionOverview(detailDOS);
        } else {
            convertibleBondPositionOverviewDO = ConvertibleBondPositionOverviewDao.Instance.queryHisRecord(bizDate);
        }
        ConvertibleBondPositionOverviewVO convertibleBondPositionOverviewVO = BeanUtil.copyProperties(convertibleBondPositionOverviewDO, ConvertibleBondPositionOverviewVO.class);

        return ApiResponse.success(Opt.ofNullable(convertibleBondPositionOverviewVO)
                .map(convertibleBondPositionOverviewVO1 -> {
                    //最新市值  保留2位小数
                    convertibleBondPositionOverviewVO1.setCurrentMarketValue(Opt.ofNullable(convertibleBondPositionOverviewVO1.getCurrentMarketValue()).map(v -> v.setScale(2, RoundingMode.HALF_UP)).orElse(null));
                    //当日规模变动（亿） 保留2位小数
                    convertibleBondPositionOverviewVO1.setChangeMarketValue(Opt.ofNullable(convertibleBondPositionOverviewVO1.getChangeMarketValue()).map(v -> v.setScale(2, RoundingMode.HALF_UP)).orElse(null));
                    //当日实时盈亏(万) 保留2位小数
                    convertibleBondPositionOverviewVO1.setCurrentProfitAndLoss(Opt.ofNullable(convertibleBondPositionOverviewVO1.getCurrentProfitAndLoss()).map(v -> v.setScale(2, RoundingMode.HALF_UP)).orElse(null));
                    return convertibleBondPositionOverviewVO1;
                })
                .orElse(new ConvertibleBondPositionOverviewVO()));
    }

    public ApiResponse<ConvertibleBondVO> queryBond(ConvertibleBondREQ req) {
        ApiResponse<ConvertibleBondVO> response = new ApiResponse<>();
        if (!O32Enable) {
            return response.setData(Collections.emptyList()).setOk();
        }
        Integer yyyyMMdd = Integer.parseInt(TimeTool.CURRENT.getCunrrentyyyyMMdd());
        Integer bizDate = req.getBizDate();
        Integer pageNo = req.getPageNo();
        Integer pageSize = req.getPageSize();
        String searchKey = req.getSearchKey();
        if (Objects.equals(yyyyMMdd, bizDate) && nowIsBefore930()) {
            bizDate = getLastBizDateOfXSHG(bizDate);
            LogTool.info("可转债持仓债券早于9点半，查询昨日数据！");
        }
        if (Objects.equals(yyyyMMdd, bizDate)) {
            //查询日不是交易日
            try {
                if (!ibTradeDateService.isTradeDateWithMarketCode(bizDate, TradeMarketCodeEnum.XSHG)) {
                    LogTool.info("查询日不是交易日，债券代码集合为空");
                    return response.setData(Collections.emptyList()).setOk();
                }
            } catch (Exception e) {
                LogTool.error("可转债损益-获取交易日失败", e);
            }
            //构造返回对象
            List<ConvertibleBondVO> retVOS = new ArrayList<>();
            List<StockInfoVO> stockInfoVOS = RedisUtil.getForValue(CommonConstant.PREFIX_CONVERTIBLE_BOND_AMOUNT, new TypeToken<List<StockInfoVO>>() {
            });
            if (ObjectUtil.isNull(stockInfoVOS)) {
                LogTool.warn("可转债损益报表-redis中O32持仓数据-债券代码集合为null");
                stockInfoVOS = selectConvertibleBondAmount();
            }
            if (CollUtil.isEmpty(stockInfoVOS)) {
                LogTool.warn("可转债损益报表-查询O32数据库 持仓数据-债券代码集合为空");
                return response.setData(Collections.emptyList()).setOk();
            }
            if (StrUtil.isNotBlank(searchKey)) {
                stockInfoVOS = stockInfoVOS.stream()
                        .filter(v -> (StrUtil.isNotBlank(v.getVcInterCode()) && ConvertUtil.tryParseConvertibleBondSuffix(v.getVcInterCode()).contains(searchKey))
                                || (StrUtil.isNotBlank(v.getVcStockName()) && v.getVcStockName().contains(searchKey)))
                        .collect(Collectors.toList());
            }
            stockInfoVOS.forEach(v -> {
                ConvertibleBondVO convertibleBondVO = new ConvertibleBondVO();
                convertibleBondVO.setBondMarketCode(ConvertUtil.tryParseConvertibleBondSuffix(v.getVcInterCode()));
                convertibleBondVO.setBondName(v.getVcStockName());
                retVOS.add(convertibleBondVO);
            });
            return response.setData(ApiUtil.getPageRecord(retVOS, pageNo, pageSize)).setCount(retVOS.size()).setOk();
        } else {
            Map<String, Object> conditions = new HashMap<>();
            conditions.put("pageNo", pageNo);
            conditions.put("pageSize", pageSize);
            conditions.put("searchKey", searchKey);
            conditions.put("bizDate", bizDate);
            Integer finalBizDate = bizDate;
            return ApiUtil.getCustomPage(conditions, params -> ConvertibleBondPositionDetailDao.Instance.queryBond(finalBizDate, searchKey));
        }
    }

    public ApiResponse<ConvertibleBondAllInfoVO> queryAllInfo(ConvertibleBondPositionDetailREQ req) {
        ConvertibleBondAllInfoVO bondAllInfoVO = new ConvertibleBondAllInfoVO();
        ApiResponse<ConvertibleBondAllInfoVO> apiResponse = new ApiResponse<>();
        apiResponse.setOk();
        ConvertibleBondPositionDetailREQ positionDetailREQ = new ConvertibleBondPositionDetailREQ();
        Integer bizDate = req.getBizDate();
        positionDetailREQ.setBizDate(bizDate);
        positionDetailREQ.setPageNo(1);
        positionDetailREQ.setPageSize(9999999);
        List<ConvertibleBondPositionDetailDO> bondPositionDetailDOS = selectConvertibleBondPositionDetailDO(positionDetailREQ, false).getData();
        //处理分页逻辑+bondMarketCode筛选 + fundId基金筛选
        ApiResponse<ConvertibleBondPositionDetailVO> bondPositionDetailVOApiResponse = handlePageAndBondMarketCode(bondPositionDetailDOS, req);
        bondAllInfoVO.setConvertibleBondPositionDetailVO(bondPositionDetailVOApiResponse);

        //overview概览页
        ApiResponse<ConvertibleBondPositionOverviewVO> convertibleBondPositionOverviewVOApiResponse = handleConvertibleBondPositionOverview(req.getBizDate(), bondPositionDetailDOS);
        bondAllInfoVO.setConvertibleBondPositionOverviewVO(convertibleBondPositionOverviewVOApiResponse);

        //债项评级统计
        ApiResponse<ConvertibleBondRatingVO> convertibleBondRatingVOApiResponse = handleConvertibleBondRating(bondPositionDetailDOS);
        bondAllInfoVO.setConvertibleBondVO(convertibleBondRatingVOApiResponse);
        apiResponse.setData(bondAllInfoVO);
        return apiResponse;
    }

    private ApiResponse<ConvertibleBondRatingVO> handleConvertibleBondRating(List<ConvertibleBondPositionDetailDO> bondPositionDetailDOS) {
        ApiResponse<ConvertibleBondRatingVO> response = new ApiResponse<>();
        response.setOk();
        if (CollUtil.isEmpty(bondPositionDetailDOS)){
            return response;
        }
        List<ConvertibleBondRatingVO> resList = new ArrayList<>();
        List<ConvertibleBondPositionDetailDO> positionDetailDOS = bondPositionDetailDOS.stream()
                .filter(v -> StrUtil.isNotBlank(v.getBDRating())).collect(Collectors.toList());
        if (CollUtil.isEmpty(positionDetailDOS)) {
            return response.setData(Collections.emptyList());
        }

        BigDecimal marketValTotal = positionDetailDOS.stream().map(ConvertibleBondPositionDetailDO::getCurrentMarketValue)
                .filter(ObjectUtil::isNotNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        Map<String, List<ConvertibleBondPositionDetailDO>> resMap = positionDetailDOS.stream()
                .collect(Collectors.groupingBy(ConvertibleBondPositionDetailDO::getBDRating));
        resMap.forEach((key, val) -> {
            if (StrUtil.isEmpty(key)) {
                return;
            }
            BDRatingEnum bdRatingEnum = BDRatingEnum.getEnumByCode(key);
            if (ObjectUtil.isNull(bdRatingEnum)) {
                LogTool.warn("可转债损益报表-对应BDRating={}不存在枚举", key);
                return;
            }
            ConvertibleBondRatingVO convertibleBondRatingVO = new ConvertibleBondRatingVO();
            convertibleBondRatingVO.setBDRating(bdRatingEnum.getName());
            convertibleBondRatingVO.setBDRatingSort(bdRatingEnum.getValue());
            BigDecimal singleVal = BigDecimal.ZERO;
            if (CollUtil.isNotEmpty(val)) {
                singleVal = val.stream().map(ConvertibleBondPositionDetailDO::getCurrentMarketValue)
                        .filter(ObjectUtil::isNotNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            convertibleBondRatingVO.setTotalMarketValue(BigDecimalUtil.divide(singleVal, NumberUnitEnum.HUNDRED_MILLION.getBigDecimalNum(), 4).toString());
            BigDecimal initRate = BigDecimalUtil.divide(singleVal, marketValTotal, 4);
            convertibleBondRatingVO.setRate(BigDecimalUtil.multiply(initRate, NumberUnitEnum.HUNDRED.getBigDecimalNum(), 2).toString());
            resList.add(convertibleBondRatingVO);
        });
        return response.setData(resList.stream().sorted(Comparator.comparing(ConvertibleBondRatingVO::getBDRatingSort)).collect(Collectors.toList()));
    }

    /**
     * 概览页逻辑处理
     *
     * @param bizDate
     * @param bondPositionDetailDOS
     * @return
     */
    public ApiResponse<ConvertibleBondPositionOverviewVO> handleConvertibleBondPositionOverview(Integer bizDate, List<ConvertibleBondPositionDetailDO> bondPositionDetailDOS) {
        Integer yyyyMMdd = Integer.parseInt(TimeTool.CURRENT.getCunrrentyyyyMMdd());
        if (Objects.equals(yyyyMMdd, bizDate) && nowIsBefore930()) {
            bizDate = getLastBizDateOfXSHG(bizDate);
            LogTool.info("可转债实时损益早于9点半，查询昨日数据！bizDate={}", bizDate);
        }
        ConvertibleBondPositionOverviewDO convertibleBondPositionOverviewDO;
        if (Objects.equals(yyyyMMdd, bizDate)) {
            bondPositionDetailDOS.forEach(vo -> {
                vo.setBizDate(Integer.parseInt(TimeTool.CURRENT.getCunrrentyyyyMMdd()));
                vo.setEID(RandomUtil.randomUUID());
            });
            convertibleBondPositionOverviewDO = calPositionOverview(bondPositionDetailDOS);
        } else {
            convertibleBondPositionOverviewDO = ConvertibleBondPositionOverviewDao.Instance.queryHisRecord(bizDate);
        }
        ConvertibleBondPositionOverviewVO convertibleBondPositionOverviewVO = BeanUtil.copyProperties(convertibleBondPositionOverviewDO, ConvertibleBondPositionOverviewVO.class);

        return ApiResponse.success(Opt.ofNullable(convertibleBondPositionOverviewVO)
                .map(convertibleBondPositionOverviewVO1 -> {
                    //最新市值  保留2位小数
                    convertibleBondPositionOverviewVO1.setCurrentMarketValue(Opt.ofNullable(convertibleBondPositionOverviewVO1.getCurrentMarketValue()).map(v -> v.setScale(2, RoundingMode.HALF_UP)).orElse(null));
                    //当日ETF市值  保留2位小数
                    convertibleBondPositionOverviewVO1.setEtfMarketValue(Opt.ofNullable(convertibleBondPositionOverviewVO1.getEtfMarketValue()).map(v -> v.setScale(2, RoundingMode.HALF_UP)).orElse(null));
                    //当日债券市值  保留2位小数
                    convertibleBondPositionOverviewVO1.setBondMarketValue(Opt.ofNullable(convertibleBondPositionOverviewVO1.getBondMarketValue()).map(v -> v.setScale(2, RoundingMode.HALF_UP)).orElse(null));
                    //当日规模变动（亿） 保留2位小数
                    convertibleBondPositionOverviewVO1.setChangeMarketValue(Opt.ofNullable(convertibleBondPositionOverviewVO1.getChangeMarketValue()).map(v -> v.setScale(2, RoundingMode.HALF_UP)).orElse(null));
                    //当日实时盈亏(万) 保留2位小数
                    convertibleBondPositionOverviewVO1.setCurrentProfitAndLoss(Opt.ofNullable(convertibleBondPositionOverviewVO1.getCurrentProfitAndLoss()).map(v -> v.setScale(2, RoundingMode.HALF_UP)).orElse(null));
                    return convertibleBondPositionOverviewVO1;
                })
                .orElse(new ConvertibleBondPositionOverviewVO()));
    }


    /**
     * 处理分页逻辑+bondMarketCode筛选
     *
     * @param req
     * @return
     */
    private ApiResponse<ConvertibleBondPositionDetailVO> handlePageAndBondMarketCode(List<ConvertibleBondPositionDetailDO> originBondPositionDetailDOS, ConvertibleBondPositionDetailREQ req) {
        ApiResponse<ConvertibleBondPositionDetailVO> response = new ApiResponse<>();
        response.setOk();
        if(CollUtil.isEmpty(originBondPositionDetailDOS)){
            return response;
        }
        //筛选bondMarketCode
        String bondMarketCode = req.getBondMarketCode();
        Integer pageNo = req.getPageNo();
        Integer pageSize = req.getPageSize();
        List<ConvertibleBondPositionDetailDO> handleList = new ArrayList<>(originBondPositionDetailDOS);

        Integer fundId;
        if(StrUtil.isNotBlank(req.getFundId()) && NumberUtil.isInteger(req.getFundId())){
            fundId = Integer.parseInt(req.getFundId());
        } else {
            fundId = null;
        }
        // 根据bondMarketCode过滤
        if (StrUtil.isNotBlank(bondMarketCode)) {
            handleList = handleList.stream()
                    .filter(v -> (StrUtil.isNotBlank(v.getBondMarketCode()) && v.getBondMarketCode().equals(bondMarketCode)))
                    .collect(Collectors.toList());
        }

        // 根据fundId过滤
        if (ObjectUtil.isNotNull(fundId)) {
            handleList = handleList.stream()
                    .filter(v -> (ObjectUtil.isNotNull(v.getFundId()) && v.getFundId().equals(fundId)))
                    .collect(Collectors.toList());
        }

        if (CollUtil.isEmpty(handleList)) {
            return response;
        }
        //排序条件
        List<EmSort> querySorts = new ArrayList<>();
        //添加默认排序
        if (CollUtil.isEmpty(req.getSorts())) {
            EmSort sort = new EmSort("bondMarketCode", true);
            querySorts.add(sort);
        } else {
            querySorts.addAll(req.getSorts());
        }
        if (querySorts.size() != 1) {
            LogTool.warn("可转债持仓明细排序参数不为1！");
        }
        //排序
        compareListByQuerySorts(querySorts, handleList);
        List<ConvertibleBondPositionDetailDO> resList = ApiUtil.getPageRecord(handleList, pageNo, pageSize);
        int countSize = CollUtil.size(handleList);
        //汇总结果
        resList.add(calPositionTotal(resList));
        if (CollUtil.isNotEmpty(resList)) {
            List<ConvertibleBondPositionDetailVO> convertibleBondPositionDetailVOS = resList.stream().map(ConvertibleBondPositionDetailVO::fromDetailDO).collect(Collectors.toList());
            response.setData(convertibleBondPositionDetailVOS).setCount(countSize);
        }
        return response;
    }


}
