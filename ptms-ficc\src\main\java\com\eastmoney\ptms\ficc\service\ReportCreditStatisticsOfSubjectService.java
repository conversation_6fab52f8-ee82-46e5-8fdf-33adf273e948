package com.eastmoney.ptms.ficc.service;

import cn.hutool.core.util.StrUtil;
import com.eastmoney.digi.common.util.CollectUtil;
import com.eastmoney.digi.core.api.ApiResponse;
import com.eastmoney.digi.core.err.exception.DAssert;
import com.eastmoney.ptms.data.common.PtmsErrCode;
import com.eastmoney.ptms.data.ficc.DO.ReportCreditLineManagementDO;
import com.eastmoney.ptms.data.ficc.DO.ReportCreditStatisticsOfSubjectDO;
import com.eastmoney.ptms.data.ficc.REQ.CommonQueryDateREQ;
import com.eastmoney.ptms.ficc.choice.IBTradeDateService;
import com.eastmoney.ptms.ficc.dao.ReportCreditLineManagementDao;
import com.eastmoney.ptms.ficc.tool.TimeTool;
import com.eastmoney.ptms.ficc.tool.conversion.ObjConversionTool;
import com.eastmoney.ptms.ficc.tool.log.LogTool;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: wenhao
 * @Date: 2025/7/23
 * @Description:
 */
@Service
@RequiredArgsConstructor
public class ReportCreditStatisticsOfSubjectService {


    private final IBTradeDateService ibTradeDateService;

    private final ReportCreditLineManagementService reportCreditLineManagementService;

    public ApiResponse<Map<String,String>> queryT7TradeDate() {
        String currentDate = TimeTool.CURRENT.getCunrrentyyyyMMdd();
        String tPlus7Date = ibTradeDateService.getDateAddTradeDay(Integer.parseInt(currentDate), 7);
        String tNegative7Date = ibTradeDateService.getDateAddTradeDay(Integer.parseInt(currentDate), -7);
        return ApiResponse.success(CollectUtil.asMap("tPlus7Date",tPlus7Date,"tNegative7Date",tNegative7Date));
    }

    public ApiResponse<ReportCreditStatisticsOfSubjectDO> queryRes(CommonQueryDateREQ req) {
        ApiResponse<ReportCreditStatisticsOfSubjectDO> response = new ApiResponse<>();
        String startDate = req.getStartDate();
        String endDate = req.getEndDate();
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyyMMdd");
        DAssert.assertFalse(StrUtil.isEmpty(startDate) ||  StrUtil.isEmpty(endDate), PtmsErrCode.FICC_POSITION_PARAMS_REQUIRED.fillMsg("日期范围"));
        //todo 查之前可以调用下计算逻辑
        //结果数据
        Map<String, Object> param = ObjConversionTool.convertMap(req);
        param.put("groupList",reportCreditLineManagementService.determineGroupQueryPermissions());
        List<ReportCreditStatisticsOfSubjectDO> lineManagementDataList = ReportCreditLineManagementDao.Instance.queryDataByDateRange(  param);
        lineManagementDataList = lineManagementDataList.stream()
                .filter(Objects::nonNull)
                .filter(x->{
                    try {
                        String bizDate = x.getBizDate();
                        Date date = inputFormat.parse(bizDate);
                        Integer bizDateNum = Integer.valueOf(outputFormat.format(date));
                        return ibTradeDateService.isTradeDate(bizDateNum);
                    } catch (Exception e) {
                        LogTool.error("日期转换异常",e);
                    }
                    return false;
                }).collect(Collectors.toList());
        return response.setData(lineManagementDataList).setOk();
    }
}
