package com.eastmoney.ptms.ficc.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.eastmoney.digi.common.fp.types.Tuple;
import com.eastmoney.digi.common.fp.types.Tuple.Tuple2;
import com.eastmoney.digi.common.util.BigDecimalUtil;
import com.eastmoney.digi.common.util.DateUtil;
import com.eastmoney.digi.common.util.RandomUtil;
import com.eastmoney.digi.common.util.TimeTool;
import com.eastmoney.digi.core.api.ApiResponse;
import com.eastmoney.digi.core.err.exception.DAssert;
import com.eastmoney.digi.core.err.exception.DigiException;
import com.eastmoney.ptms.dao.core.UserSessionUtil;
import com.eastmoney.ptms.data.enums.BooleanEnum;
import com.eastmoney.ptms.data.enums.NumberUnitEnum;
import com.eastmoney.ptms.data.ficc.BO.BondEligibilityInfo;
import com.eastmoney.ptms.data.ficc.BO.BondInfoDataBO;
import com.eastmoney.ptms.data.ficc.BO.InvestableResultBO;
import com.eastmoney.ptms.data.ficc.BO.ReportCreditNewRegionLimitManagementBO;
import com.eastmoney.ptms.data.ficc.DO.ManualBondEntryDO;
import com.eastmoney.ptms.data.ficc.DO.OffshoreBondPositionDO;
import com.eastmoney.ptms.data.ficc.DO.ReportConfigDO;
import com.eastmoney.ptms.data.ficc.DO.ReportParamConfigDO;
import com.eastmoney.ptms.data.ficc.DTO.QuotaCalculationResultDTO;
import com.eastmoney.ptms.data.ficc.DTO.choice.BondBaseInfoRespDTO;
import com.eastmoney.ptms.data.ficc.DTO.choice.BondCDBInfoRespDTO;
import com.eastmoney.ptms.data.ficc.DTO.choice.BondFnExchangeRateRespDTO;
import com.eastmoney.ptms.data.ficc.DTO.choice.BondFxIndicatoraRespDTO;
import com.eastmoney.ptms.data.ficc.REQ.InvestableQuotaQueryREQ;
import com.eastmoney.ptms.data.ficc.REQ.ManCompanyCreditBondReportREQ;
import com.eastmoney.ptms.data.ficc.REQ.OccupancyQuotaVO;
import com.eastmoney.ptms.data.ficc.VO.*;
import com.eastmoney.ptms.ficc.choice.BondChoiceService;
import com.eastmoney.ptms.ficc.dao.*;
import com.eastmoney.ptms.ficc.enums.*;
import com.eastmoney.ptms.ficc.tool.log.LogTool;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;


import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.eastmoney.ptms.data.common.PtmsErrCode.FICC_ORDER_BOND_COMMON_ERROR;

/**
 * 个券可投额度查询服务
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Service
@RequiredArgsConstructor(onConstructor = @__(@Lazy))
public class InvestableQuotaQueryService {

    // ==================== 常量定义 ====================

    /**
     * 做市交易组别名称
     * <p>用于识别做市交易相关的业务组别，在权限检查和账户配置中使用</p>
     */
    public static final String MARKETMAKING_GROUP_NAME = "做市交易";

    /**
     * 不属于自营组的组别名称列表
     * <p>这些组别不参与自营业务的额度计算和权限控制：</p>
     * <ul>
     * <li>做市交易：专门的做市业务</li>
     * <li>可转债交易：可转债专项交易</li>
     * <li>投资顾问：投资咨询业务</li>
     * </ul>
     */
    public static final ImmutableList<String> ZY_NOT_IN_GROUP_NAMES = ImmutableList.of(MARKETMAKING_GROUP_NAME,
            "可转债交易", "投资顾问");

    /**
     * 境外债券市场标识
     * <p>用于标识境外债券，这类债券有特殊的处理逻辑：</p>
     * <ul>
     * <li>不受集中度限制</li>
     * <li>不进行Choice接口查询</li>
     * <li>使用专门的境外债券持仓表数据</li>
     * </ul>
     */
    public static final String OTHER = "OTHER";

    /**
     * 可转债二级分类标识
     * <p>根据Choice东财债券二级分类，用于识别可转债类型</p>
     * <p>可转债不适用于信用债额度管理</p>
     */
    private static final String EJFL_KZZ = "可转债";

    /**
     * 业务名称标识
     * <p>用于日志记录，便于问题追踪和业务监控</p>
     */
    private static final String BUSINESS_NAME = "【个券可投额度查询】";

    /**
     * 有效的交易状态列表
     * <p>用于查询持仓数据时过滤有效的交易状态，包括：</p>
     * <ul>
     * <li>CREATE：已创建</li>
     * <li>PREAUDIT：预审核</li>
     * <li>AUDIT：审核中</li>
     * <li>PASS：审核通过</li>
     * <li>EXE：执行中</li>
     * <li>CONFIRM：已确认</li>
     * <li>SETTLE_SUCCESS：结算成功</li>
     * <li>END_UNSETTLED：结束未结算</li>
     * <li>END_SETTLED：结束已结算</li>
     * </ul>
     */
    public static final List<String> TRADE_STATUS_LIST = ImmutableList.of(HtOrderStatusEnum.CREATE.getCode(),
            HtOrderStatusEnum.PREAUDIT.getCode(), HtOrderStatusEnum.AUDIT.getCode(), HtOrderStatusEnum.PASS.getCode(),
            HtOrderStatusEnum.EXE.getCode(), HtOrderStatusEnum.CONFIRM.getCode(),
            HtOrderStatusEnum.SETTLE_SUCCESS.getCode(), HtOrderStatusEnum.END_UNSETTLED.getCode(),
            HtOrderStatusEnum.END_SETTLED.getCode());

    // ==================== 额度相关常量 ====================

    /**
     * 不限额度标识
     * <p>当某类限额不受限制时使用此标识，在计算时会被转换为Long.MAX_VALUE</p>
     */
    private static final String UNLIMITED_QUOTA = "不限";

    /**
     * 额度名称数组
     * <p>用于生成不可投资原因说明，按固定顺序排列：</p>
     * <ol>
     * <li>T+0主体限额：当日主体剩余限额</li>
     * <li>T+N主体限额：N日后主体剩余限额</li>
     * <li>T+0区域限额：当日区域剩余限额（仅城投债）</li>
     * <li>T+N区域限额：N日后区域剩余限额（仅城投债）</li>
     * <li>T+0集中度限额：当日单券集中度限额</li>
     * <li>T+N集中度限额：N日后单券集中度限额</li>
     * <li>T+0内评限额：当日内评等级限额</li>
     * <li>T+N内评限额：N日后内评等级限额</li>
     * </ol>
     */
    private static final String[] QUOTA_NAMES = {"T+0主体限额", "T+N主体限额", "T+0区域限额", "T+N区域限额", "T+0集中度限额", "T+N集中度限额", "T+0内评限额", "T+N内评限额"};

    // ==================== 区域相关常量 ====================

    /**
     * 省级行政级别标识
     * <p>用于区域解析时判断行政级别，省级债券通常不受区域限制（重庆市除外）</p>
     */
    private static final String ADMIN_LEVEL_PROVINCE = "省级";

    /**
     * 重庆市名称
     * <p>重庆市作为直辖市，在区域限额计算中有特殊处理逻辑</p>
     */
    private static final String CHONGQING_CITY = "重庆市";

    /**
     * 成都市名称
     * <p>成都市在区域限额计算中有特殊处理逻辑，需要单独识别</p>
     */
    private static final String CHENGDU_CITY = "成都市";

    /**
     * 四川省成都市完整名称
     * <p>用于识别四川省成都市相关的区域信息，格式为"四川省-成都市"</p>
     */
    private static final String SICHUAN_CHENGDU = "四川省-成都市";

    /**
     * 区域分隔符
     * <p>用于分割区域名称的分隔符，如"浙江省-宁波市"中的"-"</p>
     */
    private static final String REGION_SEPARATOR = "-";

    // ==================== 配置相关常量 ====================

    /**
     * 行权剩余期限限制配置业务类型
     */
    public static final String EXERCISE_REMAINING_TERM_LIMIT_BIZ_TYPE = "信用债额度管理-行权剩余期限限制配置";

    /**
     * 行权剩余期限限制参数名称
     */
    public static final String EXERCISE_REMAINING_TERM_LIMIT_PARAM_NAME = "行权剩余期限限制";

    /**
     * 集中度限额配置业务类型
     */
    public static final String CONCENTRATION_LIMIT_BIZ_TYPE = "信用债额度管理-个券额度管理";

    /**
     * 集中度限额参数名称
     */
    public static final String CONCENTRATION_LIMIT_PARAM_NAME = "集中度限额";
    public static final String ZY_GROUP = "自营";
    public static final String MARKET_MAKING_GROUP = "做市";
    private static final CharSequence ADMIN_LEVEL_CITY = "地市级";
    public static final String CHONG_QING_SHENG = "重庆市-重庆市";

    // ==================== 服务依赖 ====================

    private final BondChoiceService bondChoiceService;
    private final AccountTreeService accountTreeService;
    private final ReportCreditLineManagementService reportCreditLineManagementService;
    private final CreditBondReportService creditBondReportService;
    private final ReportCreditCouponsLineManagementService reportCreditCouponsLineManagementService;
    private final ReportCrediNewCouponsLineManagementService reportCreditNewCouponsLineManagementService;
    /**
     * 查询个券可投额度
     *
     * <p>
     * 根据债券代码查询该债券的可投资额度信息，包括：
     * </p>
     * <ul>
     * <li>T+0和T+N的主体限额、区域限额、集中度限额、内评限额</li>
     * <li>债券基本信息（名称、类型、发行人、担保人等）</li>
     * <li>投资资格检查结果</li>
     * <li>各类限额的剩余可投金额</li>
     * </ul>
     *
     * @param req 查询请求，包含债券代码和查询天数等参数
     * @return ApiResponse&lt;InvestableQuotaQueryResponseVO&gt; 查询结果，包含可投额度详细信息
     */
    public ApiResponse<InvestableQuotaQueryResponseVO> queryInvestableQuota(InvestableQuotaQueryREQ req) {
        String traceId = RandomUtil.randomUUID();
        org.springframework.util.StopWatch sw = new org.springframework.util.StopWatch(traceId);
        try {
            // 1. 参数验证
            String bondMarketCode = req.getBondCode();
            validateRequestParameters(bondMarketCode, req.getDayNum());

            // 5. 权限检查和账户配置
            sw.start("自营&做市权限校验");
            UserPermissionResult permissionResult = checkUserPermissions();
            sw.stop();
            LogTool.info("traceId:{}, 债券代码:{}, 自营权限校验:{}, 做市权限校验:{},costMs:{}", traceId, bondMarketCode, permissionResult.hasZyPermission(),
                    permissionResult.hasMarketMakingPermission(), sw.getLastTaskTimeMillis());
            if (!permissionResult.hasAnyPermission()) {
                return ApiResponse.success(
                        InvestableQuotaQueryResponseVO.createNotApplicable(bondMarketCode, "无自营和做市权限，不可入库"));
            }

            // 2. 债券资格检查
            sw.start("个券准入规则判断");
            InvestableResultBO eligibilityResult = checkCanInvest(bondMarketCode);
            sw.stop();
            LogTool.info("traceId:{}, 债券代码:{}个券准入规则判断结果:{}，zyCanInvest:{},marketMakingCanInvest:{},reason:{}," +
                            "zyReason:{},marketMakingReason:{},manCompany:{},cost:{}",
                    traceId, bondMarketCode, eligibilityResult.isCanInvest(), eligibilityResult.isZyCanInvest(),
                    eligibilityResult.isMarketMakingCanInvest(), eligibilityResult.getReason(),
                    eligibilityResult.getZyReason(), eligibilityResult.getMarketMakingReason(),eligibilityResult.getManCompanyCreditBondReportVO()
            , sw.getLastTaskTimeMillis());

            if (!eligibilityResult.isCanInvest()) {
                String errorReason = null;
                if(permissionResult.hasZyPermission()&&permissionResult.hasMarketMakingPermission()){
                    errorReason = "自营组别不可入库:"+ eligibilityResult.getZyReason()+"，\n做市组别不可入库:"+eligibilityResult.getMarketMakingReason();
                }else if(permissionResult.hasZyPermission()){
                    errorReason = "自营组别不可入库:"+ eligibilityResult.getZyReason();
                }else if(permissionResult.hasMarketMakingPermission()){
                    errorReason = "做市组别不可入库:"+eligibilityResult.getMarketMakingReason();
                }else{
                    errorReason = eligibilityResult.getReason();
                }
                return ApiResponse.success(
                        InvestableQuotaQueryResponseVO.createNotApplicable(bondMarketCode,
                                errorReason));
            }

            // 3. 提取债券基本信息
            BondInfoDataBO bondInfo = eligibilityResult.getBondInfoDataBO();
            ManCompanyCreditBondReportVO creditInfo = eligibilityResult.getManCompanyCreditBondReportVO();
            // 4. 计算债券余额（市场流通量）
            sw.start("计算债券余额");
            BigDecimal bondBalanceNum = calculateBondBalance(traceId,bondInfo);
            sw.stop();
            LogTool.info("traceId:{}, 债券代码:{}计算债券余额结果:{},costMs:{}", traceId, bondMarketCode, bondBalanceNum, sw.getLastTaskTimeMillis());



            // 6. 准备查询所需的基础数据
            sw.start("基础数据配置查询");
            QuotaQueryData queryData = prepareQuotaQueryData(req.getDayNum(), bondBalanceNum);
            sw.stop();
            LogTool.info("traceId:{},债券代码:{}基础数据配置查询完成,quotaConfig:{},i7Quota:{},i6Quota:{},bondLimit:{},costMs:{}",
                    traceId, bondMarketCode, queryData.getQuotaConfig(), queryData.getI7Quota(), queryData.getI6Quota(),
                    queryData.getBondLimit(),sw.getLastTaskTimeMillis());
            // 7. 计算各组别可投额度
            sw.start("计算可投额度");
            List<InvestableQuotaQueryVO> result = calculateGroupQuotas(bondMarketCode, bondInfo, creditInfo,
                    permissionResult.hasZyPermission() && eligibilityResult.isZyCanInvest(),
                    permissionResult.hasMarketMakingPermission() && eligibilityResult.isMarketMakingCanInvest(),
                    permissionResult.getZyAccountSet(), permissionResult.getMarketMakingAccountSet(),
                    queryData.getCompanyRatingMap(), queryData.getManualBonds(), queryData.getQuotaConfig(),
                    queryData.getT0AmountFunction(),
                    queryData.getTnAmountFunction(),
                    queryData.getI7Quota(), queryData.getI6Quota(), queryData.getBondLimit(), eligibilityResult);
            sw.stop();
            LogTool.info("traceId:{},债券代码:{}计算可投额度完成,result:{},cost:{},totalCost:{}", traceId, bondMarketCode, result,
                    sw.getLastTaskTimeMillis(),
                    sw.getTotalTimeMillis());
            // 构建最终响应
            InvestableQuotaQueryResponseVO applicable = InvestableQuotaQueryResponseVO.createApplicable(bondMarketCode,
                    result);
            if(permissionResult.hasZyPermission() && !eligibilityResult.isZyCanInvest()){
                applicable.setReason("自营组别不可入库:"+eligibilityResult.getZyReason());
            }
            if(permissionResult.hasMarketMakingPermission() && !eligibilityResult.isMarketMakingCanInvest()){
                applicable.setReason("做市组别不可入库:"+eligibilityResult.getMarketMakingReason());
            }
            ApiResponse<InvestableQuotaQueryResponseVO> resp = ApiResponse.success(applicable);
            return resp;

        } catch (DigiException e) {
            // DigiException直接抛出，不捕获
            throw e;
        } catch (Exception e) {
            LogTool.error("traceId:{} 查询个券可投额度失败", traceId, e);
            ApiResponse<InvestableQuotaQueryResponseVO> response = new ApiResponse<>();
            return response.setError(FICC_ORDER_BOND_COMMON_ERROR.fillMsg("查询失败：" + e.getMessage()));
        }
    }

    /**
     * 获取占用额度配置信息
     *
     * <p>从信用债额度管理服务中查询占用额度的配置参数，包括各类债券的占用系数等。</p>
     * <p>如果查询失败，返回空的配置对象并记录错误日志。</p>
     *
     * <h3>配置内容包括：</h3>
     * <ul>
     * <li>同业存单占用系数</li>
     * <li>次级债、永续债、ABS占用系数</li>
     * <li>普通债券占用系数</li>
     * </ul>
     *
     * @return OccupancyQuotaVO 占用额度配置对象，查询失败时返回空对象（非null）
     * @throws RuntimeException 当配置服务不可用时可能抛出
     */
    private OccupancyQuotaVO getOccupancyQuotaVO() {
        OccupancyQuotaVO quotaVO = new OccupancyQuotaVO();
        try {
            // 查询占用额度配置，取第一条记录
            quotaVO = CollUtil.getFirst(creditBondReportService.queryOccupancyQuota()
                    .getData());
            // 如果查询结果为null，使用默认的空对象
            if (quotaVO == null) {
                quotaVO = new OccupancyQuotaVO();
                LogTool.warn(BUSINESS_NAME + "占用额度配置查询结果为空，使用默认配置");
            }
        } catch (Exception e) {
            LogTool.error(BUSINESS_NAME + "信用债额度配置获取异常", e);
            // 异常时返回空配置对象，确保不会返回null
            quotaVO = new OccupancyQuotaVO();
        }
        return quotaVO;
    }

    /**
     * 计算债券可投额度查询结果
     *
     * <p>
     * 根据债券信息、内评信息、持仓数据等计算各类限额的剩余可投金额。
     * </p>
     * <p>
     * 计算内容包括：
     * </p>
     * <ul>
     * <li>T+0和T+N的主体剩余限额</li>
     * <li>T+0和T+N的区域剩余限额</li>
     * <li>T+0和T+N的集中度剩余限额</li>
     * <li>T+0和T+N的内评剩余限额</li>
     * </ul>
     *
     * @param bondMarketCode                        债券代码
     * @param bondInfoResult                  债券基本信息
     * @param useManCompanyCreditBondReportVO 使用的公司内评信息（发行人或担保人）
     * @param zyGroupInsideSecAccNameSet      自营组内部账户名称集合
     * @param integerMap                      公司内评等级映射
     * @param manualBondEntrySet              手动入库债券列表
     * @param quotaVO                         占用额度配置
     * @param t0AmountFunction                T+0金额提取函数
     * @param tNAmountFunction                T+N金额提取函数
     * @param i7Quota                         内评7级及以上限额
     * @param i6Quota                         内评6级限额
     * @param bondLimit                       债券集中度限额
     * @param groupType                       组别类型
     * @param manCompanyQuota                 主体限额
     * @return InvestableQuotaQueryVO 可投额度查询结果
     */
    private InvestableQuotaQueryVO getQuotaResult(String bondMarketCode, BondInfoDataBO bondInfoResult,
                                                  ManCompanyCreditBondReportVO useManCompanyCreditBondReportVO,
                                                  Set<String> zyGroupInsideSecAccNameSet,
                                                  Map<String, Integer> integerMap,
                                                  List<ManualBondEntryDO> manualBondEntrySet, OccupancyQuotaVO quotaVO,

                                                  Function<PositionDataVO, String> t0AmountFunction,
                                                  Function<PositionDataVO, String> tNAmountFunction, BigDecimal i7Quota,
                                                  BigDecimal i6Quota, BigDecimal bondLimit, String groupType,
                                                  String manCompanyQuota, InvestableResultBO eligibilityResult) {
        InvestableQuotaQueryVO zyResult;
        zyResult = buildBaseResponse(bondInfoResult, eligibilityResult);
        zyResult.setGroupName(groupType);

        List<PositionDataVO> positionDataVOS = getPositionDataVOS(zyGroupInsideSecAccNameSet, integerMap,
                manualBondEntrySet,groupType);
        BigDecimal t0SubjectRemainingQuota = null;
        BigDecimal tNSubjectRemainingQuota = null;
        if (Objects.nonNull(useManCompanyCreditBondReportVO)) {
            String manCompany = useManCompanyCreditBondReportVO.getCompany();
            // 主体限额万元
            Tuple2<BigDecimal, BigDecimal> companyRemainingQuota = getCompanyRemainingQuota(manCompany, manCompanyQuota,
                    positionDataVOS,
                    quotaVO, t0AmountFunction);
            t0SubjectRemainingQuota = companyRemainingQuota._1();
            Tuple2<BigDecimal, BigDecimal> companyRemainingQuotaTN = getCompanyRemainingQuota(manCompany,
                    manCompanyQuota, positionDataVOS,
                    quotaVO, tNAmountFunction);
            tNSubjectRemainingQuota = companyRemainingQuotaTN._1();
            zyResult.setMancompanyAvailableAmountT0(t0SubjectRemainingQuota.toPlainString());
            zyResult.setMancompanyAvailableAmountTN(tNSubjectRemainingQuota.toPlainString());
            zyResult.setMancompanyOccupiedAmountT0(companyRemainingQuota._2().toPlainString());
            zyResult.setMancompanyOccupiedAmountTN(companyRemainingQuotaTN._2().toPlainString());

        }
        // 只有债券为城投时，也就是Choice接口[主体类型(YY)]输出“城投”或空值时，所属区域字段非空；
        // 否则，债券非城投，所属区域字段为空。
        // 当债券为城投债，计算函数[所属政府(YY)]、[行政级别(YY)]，用于判断所属区域：
        // a. 当[所属政府(YY)]不包含“重庆市”，但[行政级别(YY)]为省级时，所属区域字段为空。
        // b. 当[所属政府(YY)]不包含“重庆市”、“四川省-成都市”，且[行政级别(YY)]非省级时，读取[所属政府(YY)]结果中地市级区域名称。
        // c. 如果[所属政府(YY)]中包含重庆市，则根据[行政级别(YY)]，在所属区域填入“重庆市”或“重庆市-区县级”；
        // 如果[所属政府(YY)]包含“四川省-成都市”，所属区域填入“成都市”或“成都市-区县级”；其中当[行政级别(YY)]属于区县级，优先填入区县级。

        Tuple.Tuple3<String, String,String> t0regionRemainingQuotaTuple = getRegionRemainingQuota(bondInfoResult,
                positionDataVOS,
                t0AmountFunction);
        Tuple.Tuple3<String, String,String> tNregionRemainingQuotaTuple = getRegionRemainingQuota(bondInfoResult, positionDataVOS,
                tNAmountFunction);
        String t0regionRemainingQuota = t0regionRemainingQuotaTuple._1();
        String tNregionRemainingQuota = tNregionRemainingQuotaTuple._1();
        zyResult.setRegionAvailableAmountT0(t0regionRemainingQuota);
        zyResult.setRegionAvailableAmountTN(tNregionRemainingQuota);
        zyResult.setRegion(t0regionRemainingQuotaTuple._2());

        // 区域占用额度设置为"0"（非城投债）或实际占用值（城投债）
        zyResult.setRegionOccupiedAmountT0(t0regionRemainingQuotaTuple._3());
        zyResult.setRegionOccupiedAmountTN(tNregionRemainingQuotaTuple._3());

        // 集中度限额

        Tuple2<String,String> t0ConcentrationQuota = getOccupancyRemainingQuota(bondMarketCode, bondInfoResult, quotaVO,
                t0AmountFunction, positionDataVOS, bondLimit);
        Tuple2<String,String> tNConcentrationQuota = getOccupancyRemainingQuota(bondMarketCode, bondInfoResult, quotaVO,
                tNAmountFunction, positionDataVOS, bondLimit);
        zyResult.setConcentrationAvailableAmountT0(t0ConcentrationQuota._1());
        zyResult.setConcentrationAvailableAmountTN(tNConcentrationQuota._1());

        zyResult.setConcentrationOccupiedAmountT0(t0ConcentrationQuota._2());
        zyResult.setConcentrationOccupiedAmountTN(tNConcentrationQuota._2());

        // 内评限额
        // 当个券主体的内评为6及以下时，需要考虑持有主体内评为6及以下的标的总规模限额，以及持有主体内评为7及以下的标的总规模限额（在【主体额度管理】-【主体内评及授信维护】-【特殊主体内评规模限额维护】处维护）。

        String internalQuota = null;
        String tnInternalQuota = null;
        if (Objects.nonNull(useManCompanyCreditBondReportVO)) {
            Integer internalRating = useManCompanyCreditBondReportVO.getRiskInternalEvaluation();
            Tuple.Tuple3<String, String, String> internalResult = getInternalQuota(quotaVO, t0AmountFunction, i7Quota,
                    i6Quota,
                    positionDataVOS, internalRating);
            internalQuota = internalResult._1();
            Tuple.Tuple3<String, String, String> tNinternalResult = getInternalQuota(quotaVO, tNAmountFunction, i7Quota,
                    i6Quota,
                    positionDataVOS, internalRating);
            tnInternalQuota = tNinternalResult._1();


            zyResult.setEvaluationAvailableAmountT0(internalQuota);
            zyResult.setEvaluationAvailableAmountTN(tnInternalQuota);

            // 计算内评占用额度（复用 getInternalQuota 中的计算逻辑）
            zyResult.setEvaluationOccupiedAmountT0I6(internalResult._2());
            zyResult.setEvaluationOccupiedAmountTNI6(tNinternalResult._2());

            zyResult.setEvaluationOccupiedAmountT0I7(internalResult._3());
            zyResult.setEvaluationOccupiedAmountTNI7(tNinternalResult._3());
        }
        // 可投额度，8个剩余额度最小值
        QuotaCalculationResultDTO quotaResult = calculateInvestableQuota(t0SubjectRemainingQuota,
                tNSubjectRemainingQuota, t0regionRemainingQuota, tNregionRemainingQuota, t0ConcentrationQuota._1(),
                tNConcentrationQuota._1(), internalQuota, tnInternalQuota);

        zyResult.setInvestableQuota(Objects.isNull(quotaResult.getInvestableQuota()) ? "0" :
                quotaResult.getInvestableQuota()
                        .toPlainString());
        zyResult.setCanEntry(Boolean.TRUE.equals(quotaResult.getCanInvest()) ? "是" : "否");
        return zyResult;
    }

    /**
     * 计算最终可投额度
     *
     * <p>
     * 根据8个不同维度的限额计算最终的可投额度，取所有限额的最小值。
     * </p>
     * <p>
     * 计算逻辑：
     * </p>
     * <ul>
     * <li>解析所有额度值，处理"不限"的特殊情况</li>
     * <li>取8个限额的最小值作为最终可投额度</li>
     * <li>如果最小值≤0，则不可投资并生成原因说明</li>
     * <li>如果最小值>0，则可投资</li>
     * </ul>
     *
     * @param t0SubjectQuota       T+0主体剩余额度
     * @param tnSubjectQuota       T+N主体剩余额度
     * @param t0RegionQuota        T+0区域剩余额度（可能为"不限"字符串）
     * @param tnRegionQuota        T+N区域剩余额度（可能为"不限"字符串）
     * @param t0ConcentrationQuota T+0集中度限额（可能为"不限"字符串）
     * @param tnConcentrationQuota T+N集中度限额（可能为"不限"字符串）
     * @param t0RatingQuota        T+0内评限额（可能为"不限"字符串）
     * @param tnRatingQuota        T+N内评限额（可能为"不限"字符串）
     * @return QuotaCalculationResultDTO 计算结果，包含可投额度和是否可投资标识
     */
    private QuotaCalculationResultDTO calculateInvestableQuota(BigDecimal t0SubjectQuota, BigDecimal tnSubjectQuota,
                                                               String t0RegionQuota, String tnRegionQuota,
                                                               String t0ConcentrationQuota, String tnConcentrationQuota,
                                                               String t0RatingQuota, String tnRatingQuota) {

        QuotaCalculationResultDTO result = new QuotaCalculationResultDTO();

        // 解析所有额度值
        BigDecimal t0Subject = parseQuotaValue(t0SubjectQuota);
        BigDecimal tnSubject = parseQuotaValue(tnSubjectQuota);
        BigDecimal t0Region = parseQuotaValue(t0RegionQuota);
        BigDecimal tnRegion = parseQuotaValue(tnRegionQuota);
        BigDecimal t0Concentration = parseQuotaValue(t0ConcentrationQuota);
        BigDecimal tnConcentration = parseQuotaValue(tnConcentrationQuota);
        BigDecimal t0Rating = parseQuotaValue(t0RatingQuota);
        BigDecimal tnRating = parseQuotaValue(tnRatingQuota);
        BigDecimal minQuota;
        if (Objects.isNull(t0Subject) || Objects.isNull(tnSubject) || Objects.isNull(t0Region) || Objects.isNull(
                tnRegion)
                || Objects.isNull(t0Concentration) || Objects.isNull(tnConcentration) || Objects.isNull(t0Rating)
                || Objects.isNull(tnRating)) {
            minQuota = null;
        } else {
            // 计算8个值的最小值
            minQuota = BigDecimalUtil.min(t0Subject, tnSubject);
            minQuota = BigDecimalUtil.min(minQuota, t0Region);
            minQuota = BigDecimalUtil.min(minQuota, tnRegion);
            minQuota = BigDecimalUtil.min(minQuota, t0Concentration);
            minQuota = BigDecimalUtil.min(minQuota, tnConcentration);
            minQuota = BigDecimalUtil.min(minQuota, t0Rating);
            minQuota = BigDecimalUtil.min(minQuota, tnRating);
        }


        // 设置可投额度
        result.setInvestableQuota(minQuota);

        // 判断是否可投资
        if (Objects.isNull(minQuota) || minQuota.compareTo(BigDecimal.ZERO) <= 0) {
            result.setCanInvest(false);
            result.setReason(generateInvestabilityReason(t0Subject, tnSubject, t0Region, tnRegion, t0Concentration,
                    tnConcentration, t0Rating, tnRating));
        } else {
            result.setCanInvest(true);
            result.setReason(null);
        }

        return result;
    }

    /**
     * 解析额度值，统一处理不同类型的额度数据
     *
     * <p>
     * 处理逻辑：
     * </p>
     * <ul>
     * <li>null值：返回0</li>
     * <li>BigDecimal类型：直接返回</li>
     * <li>字符串"不限"：返回Long.MAX_VALUE（表示无限制）</li>
     * <li>其他字符串：尝试解析为BigDecimal，失败则返回0</li>
     * </ul>
     *
     * @param quotaValue 额度值，可能是BigDecimal、String或null
     * @return BigDecimal 解析后的额度值，"不限"返回Long.MAX_VALUE，解析失败返回0
     */
    private BigDecimal parseQuotaValue(Object quotaValue) {
        if (Objects.isNull(quotaValue)) {
            return null;
        }

        if (quotaValue instanceof BigDecimal) {
            return (BigDecimal) quotaValue;
        }

        if (quotaValue instanceof String) {
            String strValue = (String) quotaValue;
            if (UNLIMITED_QUOTA.equals(strValue)) {
                // "不限"视为无限大，使用Long.MAX_VALUE
                return BigDecimal.valueOf(Long.MAX_VALUE);
            }
            return BigDecimalUtil.tryParseDecimal(strValue, BigDecimal.ZERO);
        }

        return BigDecimal.ZERO;
    }

    /**
     * 生成不可投资的原因说明
     *
     * <p>
     * 检查8个限额中哪些小于等于0，生成相应的不可投资原因。
     * </p>
     * <p>
     * 限额顺序：T+0主体限额、T+N主体限额、T+0区域限额、T+N区域限额、
     * T+0集中度限额、T+N集中度限额、T+0内评限额、T+N内评限额
     * </p>
     *
     * @param quotas 8个额度值的数组，按固定顺序传入
     * @return String 不可投资原因，格式如"T+0主体限额、T+N区域限额小于等于0"
     */
    private String generateInvestabilityReason(BigDecimal... quotas) {
        List<String> zeroQuotas = new ArrayList<>();
        String[] quotaNames = QUOTA_NAMES;

        for (int i = 0; i < quotas.length; i++) {
            BigDecimal quota = quotas[i];
            if (Objects.isNull(quota) || quota.compareTo(BigDecimal.ZERO) <= 0) {
                // 排除"不限"的情况（Long.MAX_VALUE）
                if (Objects.isNull(quota) || quota.compareTo(BigDecimal.valueOf(Long.MAX_VALUE)) != 0) {
                    zeroQuotas.add(quotaNames[i]);
                }
            }
        }

        if (zeroQuotas.isEmpty()) {
            return "可投额度小于等于0";
        }

        return String.join("、", zeroQuotas) + "小于等于0";
    }

    /**
     * 计算内评限额剩余额度
     *
     * <p>
     * 根据发行人内评等级计算对应的内评限额剩余额度。
     * </p>
     * <p>
     * 计算规则：
     * </p>
     * <ul>
     * <li>内评7级及以上：使用i7Quota限额，null时返回"不限"</li>
     * <li>内评6级：使用i6Quota限额，null时返回"不限"</li>
     * <li>其他等级：返回"不限"</li>
     * </ul>
     *
     * @param quotaVO          占用额度配置
     * @param t0AmountFunction T+0金额提取函数
     * @param i7Quota          内评7级及以上限额配置
     * @param i6Quota          内评6级限额配置
     * @param positionDataVOS  持仓数据列表
     * @param internalRating   内评等级
     * @return String 内评限额剩余额度，"不限"表示无限制
     */
    private Tuple.Tuple3<String,String,String> getInternalQuota(OccupancyQuotaVO quotaVO,
                                                                Function<PositionDataVO, String> t0AmountFunction, BigDecimal i7Quota,
                                                                BigDecimal i6Quota, List<PositionDataVO> positionDataVOS, Integer internalRating) {
        String internalQuota = null;
        String t7OccupancyQuota = null;
        String t6OccupancyQuota = null;
        if (internalRating <= 7) {
            if (Objects.isNull(i7Quota)) {
                internalQuota = UNLIMITED_QUOTA;
            } else {
                // 查询内评为7的总限额
                List<PositionDataVO> bondPositionDataVOS = positionDataVOS.stream()
                        .filter(x -> Objects.nonNull(x.getInternalEvaluationLevel()))
                        .filter(x -> x.getInternalEvaluationLevel() <= 7)
                        .collect(Collectors.toList());

                BigDecimal i7occupancyQuota = sumPositionDataVOList(bondPositionDataVOS, x->true,
                        t0AmountFunction);
                t7OccupancyQuota = i7occupancyQuota.toPlainString();
                BigDecimal i7RemainingQuota = BigDecimalUtil.subtract(i7Quota, i7occupancyQuota, 0);
                internalQuota = i7RemainingQuota.toPlainString();
            }

        }
        if (internalRating <= 6) {
            if (Objects.isNull(i6Quota)) {
                internalQuota = UNLIMITED_QUOTA.equals(internalQuota) ? UNLIMITED_QUOTA : internalQuota;
            } else {
                // 查询内评为6的总限额
                List<PositionDataVO> bondPositionDataVOS = positionDataVOS.stream()
                        .filter(x -> Objects.nonNull(x.getInternalEvaluationLevel()))
                        .filter(x -> x.getInternalEvaluationLevel() <= 6)
                        .collect(Collectors.toList());

                BigDecimal i6occupancyQuota =sumPositionDataVOList(bondPositionDataVOS, x->true,
                        t0AmountFunction);
                t6OccupancyQuota = i6occupancyQuota.toPlainString();
                BigDecimal i6RemainingQuota = BigDecimalUtil.subtract(i6Quota, i6occupancyQuota, 0);
                if (UNLIMITED_QUOTA.equals(internalQuota)) {
                    internalQuota = i6RemainingQuota.toPlainString();
                } else {
                    internalQuota = BigDecimalUtil.min(BigDecimalUtil.tryParseDecimal(internalQuota), i6RemainingQuota)
                            .toPlainString();
                }
            }
        }
        return Tuple.tuple(internalQuota,t6OccupancyQuota,t7OccupancyQuota);
    }

    /**
     * 计算集中度限额剩余额度
     *
     * <p>
     * 根据债券类型和集中度限额配置计算剩余可投额度。
     * </p>
     * <p>
     * 特殊处理：
     * </p>
     * <ul>
     * <li>同业存单：不受集中度限制，返回"不限"</li>
     * <li>境外债：不受集中度限制，返回"不限"</li>
     * <li>bondLimit为null：无集中度限制，返回"不限"</li>
     * <li>其他情况：计算集中度限额减去已占用额度</li>
     * </ul>
     *
     * @param bondMarketCode         债券代码
     * @param bondInfoResult   债券基本信息
     * @param quotaVO          占用额度配置
     * @param t0AmountFunction T+0金额提取函数
     * @param positionDataVOS  持仓数据列表
     * @param bondLimit        债券集中度限额
     * @return String 集中度限额剩余额度，"不限"表示无限制
     */
    private Tuple2<String,String> getOccupancyRemainingQuota(String bondMarketCode, BondInfoDataBO bondInfoResult,
                                                OccupancyQuotaVO quotaVO,
                                              Function<PositionDataVO, String> t0AmountFunction,
                                              List<PositionDataVO> positionDataVOS, BigDecimal bondLimit) {
        String occupancyRemainingQuota = null;
        String occupancyQuotaStr = null;
        // 不适用于同业存单，也不适用于境外债，若不适用，则限额为”不限“
        if (bondInfoResult.isOffshore() || ChoiceBondTypeEnum.NCDS.getName()
                .equals(bondInfoResult.getBondBaseInfoRespDTO()
                        .getStrDczcYjfl()) || Objects.isNull(bondLimit)) {
            occupancyRemainingQuota = UNLIMITED_QUOTA;
        } else {
            // 单只债券持仓面额
            BigDecimal occupancyQuota= sumPositionDataVOList(positionDataVOS, x->StrUtil.equals(x.getBondMarketCode()
                    ,bondMarketCode), t0AmountFunction);
            occupancyQuotaStr = occupancyQuota.toPlainString();
            occupancyRemainingQuota = BigDecimalUtil.subtract(bondLimit, occupancyQuota, 0)
                    .toPlainString();
        }
        return Tuple.tuple(occupancyRemainingQuota,occupancyQuotaStr);
    }

    /**
     * 查询债券是否满足个券准入规则
     *
     * <p>
     * 根据债券代码检查该债券是否符合投资准入条件，包括：
     * </p>
     * <ul>
     * <li>债券基本信息有效性检查</li>
     * <li>境外债券特殊处理</li>
     * <li>利率债和可转债过滤</li>
     * <li>发行人和担保人信息完整性检查</li>
     * <li>手动入库债券有效期检查</li>
     * <li>债券入库规则验证（内评等级、永续债、次级债等）</li>
     * <li>担保人内评等级比较</li>
     * </ul>
     *
     * @param bondMarketCode 债券市场代码，格式如"123456.SH"或"123456.SZ"
     * @return InvestableResultBO 投资资格检查结果，包含是否可投资及相关信息
     */
    public InvestableResultBO checkCanInvest(String bondMarketCode) {
        BondInfoDataBO bondInfoResult = getBondBasicInfoWithChoice(bondMarketCode);
        if (Objects.isNull(bondInfoResult)) {
            return InvestableResultBO.notInvestable("暂无债券基本信息");
        }
        if (bondInfoResult.isOffshore()) {
            InvestableResultBO investableResultBO = InvestableResultBO.canInvestable();
            investableResultBO.setBondInfoDataBO(bondInfoResult);
            return investableResultBO;
        }

        // 获取发行人和担保人内评信息
        String instName = bondInfoResult.getBondBaseInfoRespDTO()
                .getInstName();
        String wtyCompname = bondInfoResult.getBondBaseInfoRespDTO()
                .getWtyCompname();
        List<ManualBondEntryDO> manualBond = ManualBondEntryDao.Instance.selectByBondCode(bondMarketCode);

        ManCompanyCreditBondReportVO companyInternalRating = getManCompanyCreditBond(instName);
        Integer riskInternalEvaluation = Opt.ofNullable(companyInternalRating)
                .map(ManCompanyCreditBondReportVO::getRiskInternalEvaluation)
                .get();

        ManCompanyCreditBondReportVO wtyCompnameCompanyInternalRating = getManCompanyCreditBond(wtyCompname);
        Integer wtyCompanyInternalRating = Opt.ofNullable(wtyCompnameCompanyInternalRating)
                .map(ManCompanyCreditBondReportVO::getRiskInternalEvaluation)
                .get();

        // 使用BondInfoDataBO的toBondEligibilityInfo方法创建封装对象
        BondEligibilityInfo bondEligibilityInfo = bondInfoResult.toBondEligibilityInfo(riskInternalEvaluation,
                wtyCompanyInternalRating);

        InvestableResultBO investableResult = getInvestableResult(bondEligibilityInfo, manualBond);
        investableResult.setBondInfoDataBO(bondInfoResult);
        investableResult.setManCompanyCreditBondReportVO(
                investableResult.isIssuerMancompany() ? companyInternalRating : wtyCompnameCompanyInternalRating);
        return investableResult;
    }

    /**
     * 根据债券资格信息判断投资资格
     *
     * <p>
     * 核心的债券投资资格判断逻辑，包含以下检查：
     * </p>
     * <ul>
     * <li>债券信息有效性检查</li>
     * <li>境外债特殊处理（直接通过）</li>
     * <li>利率债和可转债过滤</li>
     * <li>发行人担保人信息完整性检查</li>
     * <li>手动入库债券有效期检查</li>
     * <li>债券入库规则验证</li>
     * <li>担保人内评等级比较</li>
     * </ul>
     *
     * @param bondInfo    债券资格检查信息，包含所有必要的债券属性
     * @param manualBonds 手动入库债券信息，用于判断是否在有效期内
     * @return InvestableResultBO 投资资格检查结果，包含是否可投资和相关信息
     */
    private InvestableResultBO getInvestableResult(BondEligibilityInfo bondInfo, List<ManualBondEntryDO> manualBonds) {
        if (Objects.isNull(bondInfo)) {
            return InvestableResultBO.notInvestable("债券基本信息查询失败");
        }
        if (bondInfo.isOffshore()) {
            return InvestableResultBO.canInvestable();
        }

        if (isInterestRateBondOrConvertibleBond(bondInfo.getStrDczcYjfl2021(), bondInfo.getStrDczcEjfl2021())) {
            return InvestableResultBO.notInvestable("利率债或可转债不适用");
        }

        if (StrUtil.isBlank(bondInfo.getInstName()) && StrUtil.isBlank(bondInfo.getWtyCompname())) {
            return InvestableResultBO.notInvestable("担保人发行人均为空");
        }
        ManualBondEntryDO zyManualBond = manualBonds.stream()
                .filter(x -> StrUtil.equals(ZY_GROUP, x.getGroupType()))
                .findFirst()
                .orElse(null);
        ManualBondEntryDO marketMakingManualBond = manualBonds.stream()
                .filter(x -> StrUtil.equals(MARKET_MAKING_GROUP, x.getGroupType()))
                .findFirst()
                .orElse(null);
        InvestableResultBO zyInvestableResult = checkZyCanInvest(bondInfo, zyManualBond);
        InvestableResultBO marketMakingInvestableResult = checkMarketMakingCanInvest(bondInfo, marketMakingManualBond);
        if (zyInvestableResult != null && marketMakingInvestableResult != null) {
            zyInvestableResult.setZyCanInvest(false);
            zyInvestableResult.setMarketMakingCanInvest(false);
            zyInvestableResult.setZyReason(zyInvestableResult.getReason());
            zyInvestableResult.setMarketMakingReason(marketMakingInvestableResult.getReason());
            return zyInvestableResult;
        }



        // 担保人内评等级
        boolean isIssuer = true;
        // 当债券的担保人和发行人都有内评和授信额度的情况下，如果担保人的内评等级更高，则占用担保人的授信；如果相同或发行人内评等级更高，则占用发行人的授信
        if (Objects.nonNull(bondInfo.getWtyCompnameEvaluation()) && Objects.nonNull(
                bondInfo.getInstNameEvaluation()) && bondInfo.getWtyCompnameEvaluation() > bondInfo.getInstNameEvaluation()) {
            isIssuer = false;
        }else if(Objects.nonNull(bondInfo.getWtyCompnameEvaluation()) && Objects.isNull(
                bondInfo.getInstNameEvaluation())){
            isIssuer = false;
        }
        InvestableResultBO investableResultBO = InvestableResultBO.canInvestable();
        investableResultBO.setIssuerMancompany(isIssuer);
        investableResultBO.setZyCanInvest(zyInvestableResult == null);
        investableResultBO.setMarketMakingCanInvest(marketMakingInvestableResult == null);
        investableResultBO.setZyReason(zyInvestableResult == null ? null : zyInvestableResult.getReason());
        investableResultBO.setMarketMakingReason(marketMakingInvestableResult == null ? null : marketMakingInvestableResult.getReason());
        return investableResultBO;
    }

    private InvestableResultBO checkZyCanInvest(BondEligibilityInfo bondInfo, ManualBondEntryDO manualBond) {
        // 当前日期只有大于等于入库时间且小于等于终止时间才算有效的手动入库债券
        boolean isValidManualBond = isValidManualBond(manualBond);

        if (!isValidManualBond) {
            if (Objects.isNull(bondInfo.getInstNameEvaluation())) {
                return InvestableResultBO.notInvestable("当前发行人[" + bondInfo.getInstName() + "]未维护内评等级信息");
            }
            // 通过公司名称查询内评等级
            int instNameLevel = bondInfo.getInstNameEvaluation();

            // 发行人内评1-3级时，同时为次级、永续的债券不可入库，次级债或永续债可以入库。
            // 验证债券入库规则
            String validationError = validateZyRules(instNameLevel, bondInfo.isPerpetual(),
                    bondInfo.isMdebt(), bondInfo.getRemainingTermYear(), bondInfo.isInterestRateJump(),
                    bondInfo.isHasPutOption(), bondInfo.isCtz());
            if (StrUtil.isNotBlank(validationError)) {
                return InvestableResultBO.notInvestable(validationError);
            }
        }
        return null;
    }

    private InvestableResultBO checkMarketMakingCanInvest(BondEligibilityInfo bondInfo, ManualBondEntryDO manualBond) {
        // 当前日期只有大于等于入库时间且小于等于终止时间才算有效的手动入库债券
        boolean isValidManualBond = isValidManualBond(manualBond);

        if (!isValidManualBond) {
            if (Objects.isNull(bondInfo.getInstNameEvaluation())) {
                return InvestableResultBO.notInvestable("当前发行人[" + bondInfo.getInstName() + "]未维护内评等级信息");
            }
            // 通过公司名称查询内评等级
            int instNameLevel = bondInfo.getInstNameEvaluation();

            // 发行人内评1-3级时，同时为次级、永续的债券不可入库，次级债或永续债可以入库。
            // 验证债券入库规则
            String validationError = validateMarketMakingRules(instNameLevel, bondInfo.isPerpetual(),
                    bondInfo.isMdebt(), bondInfo.getRemainingTermYear(), bondInfo.isInterestRateJump(),
                    bondInfo.isHasPutOption(), bondInfo.isCtz());
            if (StrUtil.isNotBlank(validationError)) {
                return InvestableResultBO.notInvestable(validationError);
            }
        }
        return null;
    }
    /**
     * 计算主体剩余限额
     *
     * <p>
     * 根据主体限额配置和当前持仓占用情况计算剩余可投额度。
     * </p>
     * <p>
     * 计算公式：剩余额度 = 主体限额 - 已占用额度
     * </p>
     *
     * @param manCompanyQuota  主体限额配置（万元）
     * @param positionDataVOS  持仓数据列表
     * @param quotaVO          占用额度配置
     * @param t0AmountFunction T+0金额提取函数
     * @return BigDecimal 主体剩余限额（万元）
     */
    private Tuple2<BigDecimal, BigDecimal> getCompanyRemainingQuota(String manCompany, String manCompanyQuota,
                                                                    List<PositionDataVO> positionDataVOS,
                                                                    OccupancyQuotaVO quotaVO,
                                                                    Function<PositionDataVO, String> t0AmountFunction) {
        //主体限额数据库单位为亿元
        BigDecimal subjectQuota = BigDecimalUtil.multiply(BigDecimalUtil.tryParseDecimal(manCompanyQuota),
                NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum());
        List<PositionDataVO> manCompanyPositionDataVOS = positionDataVOS.stream()
                .filter(x -> StrUtil.equals(x.getManCompany(), manCompany))
                .collect(Collectors.toList());
        BigDecimal occupyQuota = calManOccupyQuota(manCompanyPositionDataVOS, quotaVO, t0AmountFunction);

        // T+0主体剩余额度
        BigDecimal t0SubjectRemainingQuota = BigDecimalUtil.subtract(subjectQuota, occupyQuota, 0);
        return Tuple2.tuple(t0SubjectRemainingQuota,occupyQuota);
    }

    /**
     * 计算区域剩余限额
     *
     * <p>
     * 仅对城投债进行区域限额计算，非城投债返回"不限"。
     * </p>
     * <p>
     * 区域获取优先级：
     * </p>
     * <ul>
     * <li>境外债：从OffshoreBondPosition表获取区域信息</li>
     * <li>境内债：从债券基本信息解析区域</li>
     * </ul>
     * <p>
     * 区域限额计算：根据区域类型（省级/地级市）分别计算限额
     * </p>
     *
     * @param bondInfoResult  债券基本信息
     * @param positionDataVOS 持仓数据列表
     * @param amountFunction  金额提取函数
     * @return String 区域剩余限额，"不限"表示无限制
     */
    private Tuple.Tuple3<String, String,String> getRegionRemainingQuota(BondInfoDataBO bondInfoResult,
                                                                 List<PositionDataVO> positionDataVOS,
                                                                 Function<PositionDataVO, String> amountFunction) {
        String regionRemainingQuota = null;
        String region = null;
        String occupyQuota = null;
        if (bondInfoResult.isCtz()) {

            if (bondInfoResult.isOffshore()) {
                region = Opt.ofNullable(bondInfoResult)
                        .map(BondInfoDataBO::getOffshoreBondPositionDO)
                        .map(OffshoreBondPositionDO::getRegion)
                        .get();
            } else {
                String yyaArea = Opt.ofNullable(bondInfoResult)
                        .map(BondInfoDataBO::getBondBaseInfoRespDTO)
                        .map(BondBaseInfoRespDTO::getYyaArea)
                        .get();
                // 行政级别
                String yyaAdminLevel = Opt.ofNullable(bondInfoResult)
                        .map(BondInfoDataBO::getBondBaseInfoRespDTO)
                        .map(BondBaseInfoRespDTO::getYyaAdminLevel)
                        .get();
                region = parseArea(yyaArea, yyaAdminLevel);
            }
            if (region == null) {
                regionRemainingQuota = StrUtil.EMPTY;
            } else {
                // 计算区域额度
                Map<String, List<PositionDataVO>> areaPositions = positionDataVOS.stream()
                        .filter(x -> x.isCtz())
                        .map(x -> {
                            if (!OTHER.equals(x.getMarket())) {
                                x.setArea(parseArea(x.getYyaArea(), x.getYyaAdminLevel()));
                            }
                            return x;
                        })
                        .filter(x -> Objects.nonNull(x.getArea()))
                        .collect(Collectors.groupingBy(x -> x.getArea()));
                if (region.contains(REGION_SEPARATOR) && (region.contains(CHENGDU_CITY) || region.contains(
                        CHONGQING_CITY))) {
                    String containesRegion = region.contains(CHENGDU_CITY) ? CHENGDU_CITY : CHONGQING_CITY;
                    List<PositionDataVO> shiPositions = areaPositions.get(containesRegion);
                    List<PositionDataVO> shiCountyPositions = areaPositions.get(region);
                    BigDecimal shiSum = sumPositionDataVOList(shiPositions, x -> true, amountFunction);
                    BigDecimal shiCountySum = sumPositionDataVOList(shiCountyPositions, x -> true, amountFunction);
                    occupyQuota = "市："+shiSum+"区："+shiCountySum;
                    // 查询区域限额
                    BigDecimal shiQuota = queryRegionLimit(containesRegion);
                    BigDecimal shiCountyQuota = queryRegionLimit(region);

                    // 区域限额小于0为不限，没有的话限额为空
                    if (Objects.nonNull(shiQuota) && shiQuota.compareTo(BigDecimal.ZERO) < 0) {
                        shiQuota = BigDecimal.valueOf(Long.MAX_VALUE);
                    }
                    if (Objects.nonNull(shiCountyQuota) && shiCountyQuota.compareTo(BigDecimal.ZERO) < 0) {
                        shiCountyQuota = BigDecimal.valueOf(Long.MAX_VALUE);
                    }
                    if (shiQuota == null || shiCountyQuota == null) {
                        regionRemainingQuota = StrUtil.EMPTY;
                    } else {
                        // 剩余额度
                        BigDecimal shiRemainingQuota = BigDecimalUtil.subtract(shiQuota, shiSum, 0);
                        BigDecimal shiCountyRemainingQuota = BigDecimalUtil.subtract(shiCountyQuota, shiCountySum, 0);
                        regionRemainingQuota = BigDecimalUtil.min(shiRemainingQuota, shiCountyRemainingQuota)
                                .toPlainString();
                    }


                } else {
                    List<PositionDataVO> shiCountyPositions = areaPositions.get(region);
                    BigDecimal shiCountySum = sumPositionDataVOList(shiCountyPositions, x -> true, amountFunction);
                    occupyQuota = shiCountySum.toPlainString();
                    // 查询区域限额
                    BigDecimal shiCountyQuota = queryRegionLimit(region);
                    if (Objects.isNull(shiCountyQuota)) {
                        regionRemainingQuota = StrUtil.EMPTY;
                    } else {
                        BigDecimal shiCountyRemainingQuota = BigDecimalUtil.subtract(shiCountyQuota, shiCountySum, 0);
                        regionRemainingQuota = shiCountyRemainingQuota.toPlainString();
                    }
                }
            }
        } else {
            regionRemainingQuota = UNLIMITED_QUOTA;
        }
        return Tuple.tuple(regionRemainingQuota, region, occupyQuota);
    }

    /**
     * 解析区域信息
     *
     * <p>根据区域名称和行政级别解析出标准化的区域信息，用于区域限额计算。</p>
     *
     * <h3>业务规则说明：</h3>
     * <p>只有债券为城投时，也就是Choice接口[主体类型(YY)]输出"城投"或空值时，所属区域字段非空；
     * 否则，债券非城投，所属区域字段为空。</p>
     *
     * <h3>城投债区域解析规则：</h3>
     * <p>当债券为城投债时，根据[所属政府(YY)]、[行政级别(YY)]判断所属区域：</p>
     * <ol>
     * <li><strong>省级处理：</strong>当[所属政府(YY)]不包含"重庆市"，但[行政级别(YY)]为省级时，所属区域字段为空</li>
     * <li><strong>地市级处理：</strong>当[所属政府(YY)]不包含"重庆市"、"四川省-成都市"，且[行政级别(YY)]非省级时，
     *     读取[所属政府(YY)]结果中地市级区域名称</li>
     * <li><strong>重庆市特殊处理：</strong>如果[所属政府(YY)]中包含重庆市，则根据[行政级别(YY)]：
     *     <ul>
     *       <li>省级：填入"重庆市"</li>
     *       <li>区县级：填入"重庆市-区县级"</li>
     *     </ul>
     * </li>
     * <li><strong>成都市特殊处理：</strong>如果[所属政府(YY)]包含"四川省-成都市"：
     *     <ul>
     *       <li>省级：填入"成都市"</li>
     *       <li>区县级：填入"成都市-区县级"</li>
     *     </ul>
     * </li>
     * </ol>
     *
     * <h3>示例：</h3>
     * <pre>
     * parseArea("浙江省-宁波市", "地市级") → "宁波市"
     * parseArea("重庆市", "省级") → "重庆市"
     * parseArea("重庆市-大足区", "区县级") → "重庆市-大足区"
     * parseArea("四川省-成都市-锦江区", "区县级") → "成都市-锦江区"
     * </pre>
     *
     * @param yyaArea 原始区域名称，如"浙江省-宁波市"、"重庆市"等，可能为null
     * @param yyaAdminLevel 行政级别，如"省级"、"地市级"、"区县级"等，可能为null
     * @return String 解析后的标准化区域名称，null表示不进行区域限制
     * @see #ADMIN_LEVEL_PROVINCE 省级行政级别常量
     * @see #CHONGQING_CITY 重庆市名称常量
     * @see #SICHUAN_CHENGDU 四川省成都市完整名称常量
     */
    public String parseArea(String yyaArea, String yyaAdminLevel) {
        // 参数有效性检查
        if (StrUtil.isBlank(yyaArea)) {
            LogTool.debug("区域名称为空，返回null");
            return null;
        }

        // 判断行政级别
        boolean isSheng = StrUtil.equals(yyaAdminLevel, ADMIN_LEVEL_PROVINCE);
        boolean isShi = StrUtil.equals(yyaAdminLevel, ADMIN_LEVEL_CITY);

        String region;

        // 规则1：省级且非重庆市 → 不进行区域限制
        if (!StrUtil.contains(yyaArea, CHONGQING_CITY) && isSheng) {
            LogTool.debug("省级行政级别且非重庆市，不进行区域限制: yyaArea={}", yyaArea);
            region = null;
        }
        // 规则2：地市级处理（排除重庆市和成都市特殊情况）
        else if (!StrUtil.contains(yyaArea, CHONGQING_CITY)
                 && !StrUtil.contains(yyaArea, SICHUAN_CHENGDU)
                 && !isSheng) {
            // 提取地级市区域名称，格式如：浙江省-宁波市 → 宁波市
            if (StrUtil.contains(yyaArea, REGION_SEPARATOR) && yyaArea.split(REGION_SEPARATOR).length > 1) {
                List<String> regions = StrUtil.split(yyaArea, REGION_SEPARATOR);
                region = regions.get(1);  // 取第二部分作为地市名称
                LogTool.debug("提取地市级区域名称: {} → {}", yyaArea, region);
            } else {
                region = yyaArea;  // 如果没有分隔符，直接使用原名称
            }
        }
        // 规则3：重庆市特殊处理
        else if (StrUtil.contains(yyaArea, CHONGQING_CITY)) {
            if (isSheng) {
                region = CHONGQING_CITY;  // 省级 → "重庆市"
                LogTool.debug("重庆市省级处理: {} → {}", yyaArea, region);
            } else {
                // 处理区县级：重庆市-大足区 → 重庆市-大足区
                if (StrUtil.equals(CHONG_QING_SHENG, yyaArea)) {
                    region = CHONGQING_CITY;  // 特殊情况：重庆市-重庆市 → 重庆市
                } else {
                    List<String> regions = StrUtil.split(yyaArea, REGION_SEPARATOR);
                    if (regions.size() > 1) {
                        // 构造：重庆市-区县名
                        region = StrUtil.concat(true, regions.get(0), REGION_SEPARATOR, regions.get(1));
                        LogTool.debug("重庆市区县级处理: {} → {}", yyaArea, region);
                    } else {
                        region = yyaArea;
                    }
                }
            }
        }
        // 规则4：成都市特殊处理
        else if (StrUtil.contains(yyaArea, SICHUAN_CHENGDU)) {
            if (isSheng) {
                region = CHENGDU_CITY;  // 省级 → "成都市"
                LogTool.debug("成都市省级处理: {} → {}", yyaArea, region);
            } else {
                List<String> regions = StrUtil.split(yyaArea, REGION_SEPARATOR);
                if (regions.size() > 2) {
                    // 四川省-成都市-锦江区 → 成都市-锦江区
                    region = StrUtil.concat(true, regions.get(1), REGION_SEPARATOR, regions.get(2));
                    LogTool.debug("成都市区县级处理: {} → {}", yyaArea, region);
                } else if (isShi && regions.size() > 1) {
                    region = regions.get(1);  // 地市级取第二部分
                } else {
                    region = yyaArea;
                }
            }
        }
        // 规则5：其他未知情况
        else {
            LogTool.warn("所属政府解析遇到未知情况,region: {}, adminLevel: {}", yyaArea, yyaAdminLevel);
            region = yyaArea;  // 保持原值
        }

        LogTool.debug("区域解析结果: yyaArea={}, yyaAdminLevel={} → region={}", yyaArea, yyaAdminLevel, region);
        return region;
    }


    /**
     * 计算总占用额度
     *
     * <p>根据不同债券类型和对应的占用系数计算总的占用额度。</p>
     *
     * <h3>计算分类：</h3>
     * <ul>
     * <li>同业存单：使用同业存单占用系数</li>
     * <li>次级债、永续债、ABS：使用次级债占用系数</li>
     * <li>其他债券：使用一般债券占用系数</li>
     * </ul>
     *
     * <h3>计算公式：</h3>
     * <pre>总占用额度 = Σ(各类债券持仓金额 × 对应占用系数)</pre>
     *
     * @param positionDataVOS 持仓数据列表，不能为null
     * @param quotaVO 占用额度配置，包含各类债券的占用系数
     * @param amountFunction 金额提取函数，用于从持仓数据中提取金额
     * @return BigDecimal 总占用额度（万元），最小值为0
     * @throws IllegalArgumentException 当参数为null时抛出
     */
    private BigDecimal calManOccupyQuota(List<PositionDataVO> positionDataVOS,
                                         OccupancyQuotaVO quotaVO, Function<PositionDataVO, String> amountFunction) {
        // 参数验证
        Objects.requireNonNull(positionDataVOS, "持仓数据列表不能为null");
        Objects.requireNonNull(quotaVO, "占用额度配置不能为null");
        Objects.requireNonNull(amountFunction, "金额提取函数不能为null");

        // 计算同业存单持仓金额（万元）
        BigDecimal ncdsOwnAmount = sumPositionDataVOList(positionDataVOS, x -> ChoiceBondTypeEnum.NCDS.getName()
                .equals(x.getRefBondType()), amountFunction);

        // 计算次级债、永续债、ABS持仓金额（万元）
        BigDecimal subordinatedAndABSOwnAmount = sumPositionDataVOList(positionDataVOS,
                x -> ChoiceBondTypeEnum.ABS.getName()
                        .equals(x.getRefBondType()) || BooleanEnum.TRUE.getName()
                        .equals(x.getPerpetualBond()) || BooleanEnum.TRUE.getName()
                        .equals(x.getStrIsmDebt()), amountFunction);

        // 计算所有持仓总金额（万元）
        BigDecimal allOwnAmount = sumPositionDataVOList(positionDataVOS, x -> true, amountFunction);

        // 计算普通债持仓金额 = 总持仓 - 同业存单 - (次级债+永续债+ABS)
        BigDecimal normalOwnAmount = BigDecimalUtil.subtract(allOwnAmount,
                BigDecimalUtil.add(ncdsOwnAmount, subordinatedAndABSOwnAmount));

        // 获取各类债券的占用系数配置
        String interbankDeposit = quotaVO.getInterbankDeposit();    // 同业存单额度系数
        String subordinatedAndABS = quotaVO.getSubordinatedAndABS(); // 次级债、永续债、ABS额度系数
        String general = quotaVO.getGeneral();                       // 普通债额度系数

        // 计算总占用额度(万元) = 各类债券持仓金额 × 对应占用系数
        BigDecimal occupyQuota = BigDecimalUtil.add(
            calOccupancyQuota(normalOwnAmount, general),
            BigDecimalUtil.add(
                calOccupancyQuota(ncdsOwnAmount, interbankDeposit),
                calOccupancyQuota(subordinatedAndABSOwnAmount, subordinatedAndABS)
            ), 0);

        return occupyQuota;
    }

    /**
     * 计算单类债券的占用额度
     *
     * <p>根据持仓金额和占用系数计算该类债券的占用额度。</p>
     *
     * <h3>计算公式：</h3>
     * <pre>占用额度 = 持仓金额 × 占用系数</pre>
     *
     * <h3>异常处理：</h3>
     * <ul>
     * <li>如果占用系数为0或解析失败，记录警告日志并返回0</li>
     * <li>如果持仓金额为null，视为0处理</li>
     * </ul>
     *
     * @param ownAmount 持仓金额（万元），可以为null
     * @param coefficient 占用系数（字符串格式），如"1.0"、"0.8"等
     * @return BigDecimal 占用额度（万元），系数无效时返回0
     */
    private BigDecimal calOccupancyQuota(BigDecimal ownAmount, String coefficient) {
        // 持仓金额为null时，直接返回0
        if (ownAmount == null) {
            return BigDecimal.ZERO;
        }

        // 解析占用系数，解析失败时使用0作为默认值
        BigDecimal coefficientDecimal = BigDecimalUtil.tryParseDecimal(coefficient, BigDecimal.ZERO);

        // 系数为0时记录警告并返回0
        if (BigDecimal.ZERO.compareTo(coefficientDecimal) == 0) {
            LogTool.warn(BUSINESS_NAME + "占用额度系数非法或为零，coefficient: {}", coefficient);
            return BigDecimal.ZERO;
        }

        // 计算占用额度 = 持仓金额 × 占用系数
        return BigDecimalUtil.multiply(ownAmount, coefficientDecimal);
    }

    /**
     * 计算持仓数据列表的金额汇总
     *
     * <p>根据指定的过滤条件和金额提取函数，计算持仓数据的总金额。</p>
     *
     * <h3>处理逻辑：</h3>
     * <ol>
     * <li>根据predicate过滤符合条件的持仓数据</li>
     * <li>使用mapper函数提取每条记录的金额字符串</li>
     * <li>将金额字符串转换为BigDecimal类型</li>
     * <li>汇总所有金额并转换为万元单位</li>
     * </ol>
     *
     * <h3>异常处理：</h3>
     * <ul>
     * <li>如果金额字符串为null，记录错误日志但继续处理</li>
     * <li>如果金额字符串无法解析，使用BigDecimal.ZERO作为默认值</li>
     * </ul>
     *
     * @param positionDataVOList 持仓数据列表，可以为空
     * @param predicate 过滤条件，用于筛选需要计算的持仓数据
     * @param mapper 金额提取函数，从持仓数据中提取金额字符串
     * @return BigDecimal 汇总金额（万元），如果列表为空则返回0
     * @throws NullPointerException 当predicate或mapper为null时抛出
     */
    public BigDecimal sumPositionDataVOList(List<PositionDataVO> positionDataVOList,
                                            Predicate<? super PositionDataVO> predicate,
                                            Function<? super PositionDataVO, String> mapper) {
        // 空列表检查
        if (CollUtil.isEmpty(positionDataVOList)) {
            return BigDecimal.ZERO;
        }

        // 参数验证
        Objects.requireNonNull(predicate, "过滤条件不能为null");
        Objects.requireNonNull(mapper, "金额提取函数不能为null");

        return BigDecimalUtil.divide(
            positionDataVOList.stream()
                .filter(predicate)                    // 1. 过滤符合条件的数据
                .map(x -> {
                    String ownMountStr = mapper.apply(x);  // 2. 提取金额字符串
                    if (Objects.isNull(ownMountStr)) {
                        LogTool.error(BUSINESS_NAME + " 持仓金额为null,positionDataVO:{}", x);
                        return BigDecimal.ZERO;        // 使用0作为默认值
                    }
                    // 3. 转换为BigDecimal，解析失败时返回0
                    return BigDecimalUtil.tryParseDecimal(ownMountStr, BigDecimal.ZERO);
                })
                .reduce(BigDecimal.ZERO, BigDecimalUtil::add),  // 4. 汇总所有金额
            NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum(),     // 5. 转换为万元单位
            0);
    }

    /**
     * 获取持仓数据列表
     *
     * <p>
     * 根据自营组账户集合查询持仓数据，并进行相关的数据处理。
     * </p>
     * <p>
     * 处理流程：
     * </p>
     * <ul>
     * <li>构建查询参数（交易状态、业务日期）</li>
     * <li>根据账户集合查询持仓数据</li>
     * <li>调用handleChoiceParam处理Choice相关参数</li>
     * <li>过滤掉主体公司为空的记录</li>
     * </ul>
     *
     * @param zyGroupInsideSecAccNameSet 自营组内部账户名称集合
     * @param integerMap                 公司内评等级映射
     * @param manualBondEntrySet         手动入库债券列表
     * @param groupType
     * @return List&lt;PositionDataVO&gt; 处理后的持仓数据列表
     */
    private List<PositionDataVO> getPositionDataVOS(Set<String> zyGroupInsideSecAccNameSet,
                                                    Map<String, Integer> integerMap,
                                                    List<ManualBondEntryDO> manualBondEntrySet, String groupType) {
        Map<String, Object> param = Maps.newHashMap();
        // 判断查询日期是否是当日，以区分查询的交易状态
        param.put("tradingStatusList", TRADE_STATUS_LIST);
        param.put("bizDate", TimeTool.CURRENT.getCunrrentyyyyMMdd());
        List<PositionDataVO> positionDataVOS = new ArrayList<>();
        if (CollUtil.isNotEmpty(zyGroupInsideSecAccNameSet)) {
            // 查询业务日期持仓信息
            param.put("insideSecAccNames", zyGroupInsideSecAccNameSet);
            positionDataVOS = reportCreditNewCouponsLineManagementService.assemblePosition(param);
        } else {
            LogTool.info(BUSINESS_NAME + "查询持仓，内部证券账号设置为空");
        }
        handleChoiceParam(positionDataVOS, integerMap, manualBondEntrySet,groupType);
        positionDataVOS = positionDataVOS.stream()
                .filter(x -> StrUtil.isNotEmpty(x.getManCompany()))
                .collect(Collectors.toList());
        return positionDataVOS;
    }



    /**
     * 处理Choice相关参数和债券资格检查
     *
     * <p>
     * 为持仓数据补充Choice债券基本信息，并进行债券资格检查。
     * </p>
     * <p>
     * 处理流程：
     * </p>
     * <ul>
     * <li>批量查询债券基本信息（境外债除外）</li>
     * <li>为每个持仓补充缺失的债券属性信息</li>
     * <li>进行债券入库规则验证</li>
     * <li>确定使用发行人还是担保人的内评等级</li>
     * <li>设置境外债的币种信息</li>
     * </ul>
     *  @param positionDataVOS    持仓数据列表
     *
     * @param leverMap           公司内评等级映射
     * @param manualBondEntrySet 手动入库债券列表
     * @param groupType
     */
    private void handleChoiceParam(List<PositionDataVO> positionDataVOS, Map<String, Integer> leverMap,
                                   List<ManualBondEntryDO> manualBondEntrySet, String groupType) {
        if (CollUtil.isEmpty(positionDataVOS)) {
            return;
        }
        // 境外债不查询Choice
        Map<String, BondBaseInfoRespDTO> baseInfoMap = getBondBaseInfoMap(positionDataVOS.stream()
                .filter(x -> !OTHER.equals(x.getMarket()))
                .collect(Collectors.toList()));
        Map<String, List<ManualBondEntryDO>> manualBondMap = CollUtil.emptyIfNull(manualBondEntrySet)
                .stream()
                .collect(Collectors.groupingBy(ManualBondEntryDO::getBondCode, Collectors.toList()));
        for (PositionDataVO positionDataVO : positionDataVOS) {
            // 处理是否次级永续为空
            String marketCode = positionDataVO.getBondMarketCode();
            if (OTHER.equals(positionDataVO.getMarket())) {
                String manCompany = positionDataVO.getManCompany();
                positionDataVO.setInternalEvaluationLevel(leverMap.get(manCompany));
                continue;
            }
            if (StrUtil.isEmpty(marketCode)) {
                continue;
            }

            // 担保人
            String writer = positionDataVO.getUnderWriter();
            // 发行人
            String instName = positionDataVO.getInstName();
            BondBaseInfoRespDTO baseInfoRespDTO = baseInfoMap.get(marketCode);
            if (StrUtil.isEmpty(writer) || StrUtil.isEmpty(instName)) {
                if (ObjectUtil.isNotNull(baseInfoRespDTO)) {
                    writer = StrUtil.isEmpty(writer) ? baseInfoRespDTO.getWtyCompname() : writer;
                    instName = StrUtil.isEmpty(instName) ? baseInfoRespDTO.getInstName() : instName;
                }
            }
            String ismDebt = positionDataVO.getStrIsmDebt();
            if (StrUtil.isEmpty(ismDebt)) {
                if (ObjectUtil.isNotNull(baseInfoRespDTO)) {
                    positionDataVO.setStrIsmDebt(baseInfoRespDTO.getStrIsMdebt());
                }
            }
            String perpetualBond = positionDataVO.getPerpetualBond();
            if (StrUtil.isEmpty(perpetualBond)) {
                if (ObjectUtil.isNotNull(baseInfoRespDTO)) {
                    positionDataVO.setPerpetualBond(baseInfoRespDTO.getPerpetualBond());
                }
            }
            if (ObjectUtil.isNotNull(baseInfoRespDTO)) {
                positionDataVO.setYySubjectType(
                        StrUtil.emptyToDefault(baseInfoRespDTO.getYySubjectType(), positionDataVO.getYySubjectType()));
                positionDataVO.setYyaAdminLevel(
                        StrUtil.emptyToDefault(baseInfoRespDTO.getYyaAdminLevel(), positionDataVO.getYyaAdminLevel()));
                positionDataVO.setYyaArea(
                        StrUtil.emptyToDefault(baseInfoRespDTO.getYyaArea(), positionDataVO.getYyaArea()));
                // b.yyaArea,b.yyaAdminLevel,b.strTermstype,b.rateClausecontent
                positionDataVO.setStrTermstype(
                        StrUtil.emptyToDefault(baseInfoRespDTO.getStrTermstype(), positionDataVO.getStrTermstype()));
                positionDataVO.setRateClausecontent(StrUtil.emptyToDefault(baseInfoRespDTO.getRateClausecontent(),
                        positionDataVO.getRateClausecontent()));
            }

            // 是否永续
            boolean isPerpetual = "是".equals(perpetualBond);
            // 是否次级
            boolean isSubordinated = "是".equals(ismDebt);
            Integer writerLevel = leverMap.get(writer);
            Integer instNameLevel = leverMap.get(instName);

            boolean isInterestRateJump = BondInfoDataBO.isInterestRateJump(positionDataVO.getRateClausecontent());
            boolean isHasPutOption = BondInfoDataBO.isHasPutOption(positionDataVO.getStrTermstype());
            positionDataVO.setCtz(BondInfoDataBO.isCtz(positionDataVO.getYySubjectType()));
            positionDataVO.setArea(parseArea(positionDataVO.getYyaArea(), positionDataVO.getYyaAdminLevel()));

            // 创建BondEligibilityInfo对象用于债券资格检查
            BondEligibilityInfo bondEligibilityInfo = createBondEligibilityInfoFromPosition(positionDataVO,
                    baseInfoRespDTO, instNameLevel, writerLevel, isPerpetual, isSubordinated, isInterestRateJump,
                    isHasPutOption);
            InvestableResultBO investableResult = getInvestableResult(bondEligibilityInfo,
                    CollUtil.emptyIfNull(manualBondMap.get(marketCode)));
            if (ZY_GROUP.equals(groupType)&&!investableResult.isZyCanInvest()){
                continue;
            }else if(MARKET_MAKING_GROUP.equals(groupType)&&!investableResult.isMarketMakingCanInvest()){
                continue;
            }

            // 当债券的担保人和发行人都有内评和授信额度的情况下，如果担保人的内评等级更高，则占用担保人的授信：如果相同或发行人内评等级更高，则占用发行人的授信;其中一个有内评及授信，则使用对应主体的授信;都没有，返回不可入库
            // 4. 手动入库债券处理
            if (investableResult.isIssuerMancompany()) {
                positionDataVO.setManCompany(instName);
                positionDataVO.setInternalEvaluationLevel(instNameLevel);
            } else {
                positionDataVO.setManCompany(writer);
                positionDataVO.setInternalEvaluationLevel(writerLevel);
            }
        }

    }

    /**
     * 从持仓数据创建债券资格检查信息对象
     *
     * <p>
     * 将持仓数据和相关的债券属性信息封装为BondEligibilityInfo对象。
     * </p>
     * <p>
     * 数据来源优先级：
     * </p>
     * <ul>
     * <li>债券基本信息：优先使用baseInfoRespDTO，回退到positionDataVO</li>
     * <li>境外债判断：通过market字段是否为"OTHER"</li>
     * <li>剩余期限：使用BondInfoDataBO.getRemainingTermNum()统一计算</li>
     * <li>城投标识：使用positionDataVO.isCtz()</li>
     * </ul>
     *
     * @param positionDataVO     持仓数据，包含债券代码、市场等基本信息
     * @param baseInfoRespDTO    债券基本信息，可能为null
     * @param instNameLevel      发行人内评等级
     * @param writerLevel        担保人内评等级
     * @param isPerpetual        是否永续债
     * @param isSubordinated     是否次级债
     * @param isInterestRateJump 是否含利率跳升机制
     * @param isHasPutOption     是否含回售条款
     * @return BondEligibilityInfo 债券资格检查信息对象
     */
    private BondEligibilityInfo createBondEligibilityInfoFromPosition(PositionDataVO positionDataVO,
                                                                      BondBaseInfoRespDTO baseInfoRespDTO,
                                                                      Integer instNameLevel, Integer writerLevel,
                                                                      boolean isPerpetual, boolean isSubordinated,
                                                                      boolean isInterestRateJump,
                                                                      boolean isHasPutOption) {

        // 判断是否境外债
        boolean isOffshore = OTHER.equals(positionDataVO.getMarket());

        // 获取债券分类信息
        String strDczcYjfl = baseInfoRespDTO != null ? baseInfoRespDTO.getStrDczcYjfl() : positionDataVO.getRefBondType();
        String strDczcEjfl2021 = baseInfoRespDTO != null ? baseInfoRespDTO.getStrDczcEjfl2021() : positionDataVO.getBondSecondType();

        // 获取发行人和担保人信息
        String instName = baseInfoRespDTO != null ? baseInfoRespDTO.getInstName() : positionDataVO.getInstName();
        String wtyCompname = baseInfoRespDTO != null ? baseInfoRespDTO.getWtyCompname() : positionDataVO.getUnderWriter();

        // 获取剩余期限
        BigDecimal remainingTermNum = BigDecimalUtil.tryParseDecimal(positionDataVO.getRemTermYear());

        // 获取城投标识
        boolean ctz = positionDataVO.isCtz();

        return new BondEligibilityInfo(isOffshore, strDczcYjfl, strDczcEjfl2021, instName, wtyCompname, isPerpetual,
                isSubordinated, remainingTermNum, isInterestRateJump, isHasPutOption, ctz, instNameLevel, writerLevel);
    }

    /**
     * 获取境外债券的币种信息
     *
     * <p>
     * 结合系统维护的持仓数据和Choice接口数据，返回境外债券对应的币种信息。
     * </p>
     * <p>
     * 数据来源优先级：
     * </p>
     * <ol>
     * <li>系统维护的境外债券持仓数据（OffshoreBondPosition表）</li>
     * <li>Choice接口的债券基本信息数据</li>
     * </ol>
     *
     * @param positionDataVOList 持仓数据列表，用于获取需要查询币种的债券代码
     * @return Map&lt;String, String&gt; 债券代码与币种的映射关系，key为债券代码，value为币种代码
     */
    public Map<String, String> getOffshoreBondCurrency(List<PositionDataVO> positionDataVOList) {
        Map<String, String> currencyMap = new HashMap<>();
        // 获取中资美债及他对应的币种
        List<OffshoreBondPositionDO> offshoreBondPositionDOS = OffshoreBondPositionDao.Instance.selectBondCodeCurrency();
        currencyMap = offshoreBondPositionDOS.stream()
                .filter(x -> ObjectUtil.isNotNull(x) && StrUtil.isNotEmpty(x.getBondMarketCode()) && StrUtil.isNotEmpty(
                        x.getCurrency()))
                .collect(Collectors.toMap(OffshoreBondPositionDO::getBondMarketCode, x -> {
                    CurrencyEnum currencyEnum = CurrencyEnum.getByCode(x.getCurrency());
                    if (ObjectUtil.isNull(currencyEnum)) {
                        return CurrencyEnum.USD.getFlag();
                    }
                    return currencyEnum.getFlag();
                }, (oldVal, newVal) -> newVal));

        List<String> maintainOffshoreBondList = offshoreBondPositionDOS.stream()
                .filter(x -> ObjectUtil.isNotNull(x) && StrUtil.isNotEmpty(x.getBondMarketCode()))
                .map(OffshoreBondPositionDO::getBondMarketCode)
                .collect(Collectors.toList());

        List<String> systemOffshoreBondList = positionDataVOList.stream()
                .filter(x -> ObjectUtil.isNotNull(x) && ReportCreditLineManagementService.OTHER.equals(
                        x.getMarket()) && CharSequenceUtil.isNotEmpty(x.getBondMarketCode()))
                .map(PositionDataVO::getBondMarketCode)
                .collect(Collectors.toList());

        maintainOffshoreBondList.addAll(systemOffshoreBondList);

        List<String> queryList = maintainOffshoreBondList.stream()
                .distinct()
                .collect(Collectors.toList());

        List<BondCDBInfoRespDTO> bondCDBInfoRespDTOS = bondChoiceService.queryChoiceCDBInfo(queryList);
        currencyMap.putAll(bondCDBInfoRespDTOS.stream()
                .filter(x -> ObjectUtil.isNotNull(x) && CharSequenceUtil.isNotEmpty(
                        x.getSecurityCode()) && CharSequenceUtil.isNotEmpty(x.getCurrency()))
                .collect(Collectors.toMap(BondCDBInfoRespDTO::getSecurityCode, BondCDBInfoRespDTO::getCurrency,
                        (oldVal, newVal) -> oldVal)));
        return currencyMap;
    }

    /**
     * 批量获取债券基本信息映射
     *
     * <p>
     * 根据持仓数据中缺失信息的债券，批量查询Choice债券基本信息。
     * </p>
     * <p>
     * 查询条件：担保人、发行人、次级债标识、永续债标识中任一为空的债券
     * </p>
     * <p>
     * 按市场分组查询，提高查询效率
     * </p>
     *
     * @param positionDataVOS 持仓数据列表
     * @return Map&lt;String, BondBaseInfoRespDTO&gt; 债券代码与基本信息的映射
     */
    private Map<String, BondBaseInfoRespDTO> getBondBaseInfoMap(List<PositionDataVO> positionDataVOS) {
        Map<String, List<PositionDataVO>> paramMap = positionDataVOS.stream()
                .filter(x -> StrUtil.isNotEmpty(x.getMarket()))
                .filter(x -> StrUtil.isEmpty(x.getUnderWriter()) || StrUtil.isEmpty(x.getInstName()) || StrUtil.isEmpty(
                        x.getStrIsmDebt()) || StrUtil.isEmpty(x.getPerpetualBond()))
                .collect(Collectors.groupingBy(PositionDataVO::getMarket));
        Map<String, BondBaseInfoRespDTO> resMap = new HashMap<>();
        for (Map.Entry<String, List<PositionDataVO>> entry : paramMap.entrySet()) {
            String key = entry.getKey();
            List<PositionDataVO> value = entry.getValue();
            HtMarketEnum byName = HtMarketEnum.getEnumByName(key);
            if (ObjectUtil.isNull(byName)) {
                continue;
            }
            List<String> bondCodeList = value.stream()
                    .map(PositionDataVO::getBondCode)
                    .filter(StrUtil::isNotEmpty)
                    .distinct()
                    .collect(Collectors.toList());
            LogTool.info(BUSINESS_NAME + "市场{}，债券代码{}基本查询", key, bondCodeList);
            try {
                // 批量获取债券基本信息map
                Map<String, BondBaseInfoRespDTO> bondBaseInfoRespDTOMap = bondChoiceService.queryBondBaseInfoFromChoice(
                                bondCodeList, byName)
                        .stream()
                        .filter(x -> StrUtil.isNotEmpty(x.getSecuCode()))
                        .collect(Collectors.toMap(BondBaseInfoRespDTO::getSecuCode, Function.identity(),
                                (oldKey, newKey) -> newKey));
                if (CollUtil.isNotEmpty(bondBaseInfoRespDTOMap)) {
                    resMap.putAll(bondBaseInfoRespDTOMap);
                }
            } catch (Exception e) {
                LogTool.error(BUSINESS_NAME + "获取choice信息失败,{}", e);
            }

        }
        return resMap;
    }

    /**
     * 获取债券基本信息（混合数据源模式）
     *
     * <p>
     * 采用混合数据源策略获取债券信息：
     * </p>
     * <ol>
     * <li>优先查询本地境外债券持仓表</li>
     * <li>如果本地无数据，则调用Choice接口查询</li>
     * </ol>
     * <p>
     * 境外债券会设置特殊的境外标识和相关属性
     * </p>
     *
     * @param bondCode 债券代码
     * @return BondInfoDataBO 债券基本信息对象，查询失败时返回null
     */
    private BondInfoDataBO getBondBasicInfoWithChoice(String bondCode) {
        Map<String, Object> param = new HashMap<>();
        param.put("bondMarketCode", bondCode);
        List<OffshoreBondPositionDO> offshoreBondPositionDOS = OffshoreBondPositionDao.Instance.selectRes(param);
        if (CollUtil.isNotEmpty(offshoreBondPositionDOS)) {
            BondInfoDataBO bondInfoWithChoiceData = new BondInfoDataBO();
            bondInfoWithChoiceData.setOffshore(true);
            bondInfoWithChoiceData.setOffshoreBondPositionDO(offshoreBondPositionDOS.get(0));
            return bondInfoWithChoiceData;
        }

        // 根据债券代码后缀解析市场枚举
        HtMarketEnum market = parseMarketFromBondCode(bondCode);

        // 提取不带后缀的债券代码
        String pureCode = extractPureBondCode(bondCode, market);

        List<String> bondCodeList = Collections.singletonList(pureCode);
        List<BondBaseInfoRespDTO> choiceResults = bondChoiceService.queryBondBaseInfoFromChoice(bondCodeList, market);

        if (choiceResults.isEmpty()) {
            return null;
        }
        List<BondFxIndicatoraRespDTO> bondFxIndicatoraRespDTOS = bondChoiceService.queryBondFxIndicatoraFromChoice(
                CollUtil.newArrayList(bondCode), new Date());
        if (CollUtil.isEmpty(bondFxIndicatoraRespDTOS)) {
            return null;
        }

        BondBaseInfoRespDTO choiceData = choiceResults.get(0);
        BondInfoDataBO bondInfoWithChoiceData = new BondInfoDataBO();
        bondInfoWithChoiceData.setBondBaseInfoRespDTO(choiceData);
        bondInfoWithChoiceData.setBondFxIndicatoraRespDTO(bondFxIndicatoraRespDTOS.get(0));

        return bondInfoWithChoiceData;
    }

    /**
     * 根据债券代码解析市场枚举
     *
     * <p>
     * 通过债券代码的后缀识别所属市场。
     * </p>
     * <p>
     * 支持的市场后缀：.SH（上交所）、.SZ（深交所）、.IB（银行间）等
     * </p>
     *
     * @param bondCode 债券代码，如"123456.SH"
     * @return HtMarketEnum 市场枚举，无法识别时抛出异常
     */
    private HtMarketEnum parseMarketFromBondCode(String bondCode) {
        if (StrUtil.isBlank(bondCode)) {
            DAssert.justFailed(FICC_ORDER_BOND_COMMON_ERROR.fillMsg("债券代码不能为空"));
        }

        // 使用HtMarketEnum的getEnumByBondSuffix方法解析市场
        HtMarketEnum market = HtMarketEnum.getEnumByBondSuffix(bondCode);

        if (market == null) {
            DAssert.justFailed(FICC_ORDER_BOND_COMMON_ERROR.fillMsg("无法识别债券代码[" + bondCode + "]的市场类型"));
        }

        return market;
    }

    /**
     * 提取不带市场后缀的纯债券代码
     *
     * <p>
     * 移除债券代码中的市场后缀，获取纯数字代码。
     * </p>
     * <p>
     * 例如："123456.SH" → "123456"
     * </p>
     *
     * @param bondCode 完整的债券代码
     * @param market   市场枚举，用于确定后缀格式
     * @return String 纯债券代码，参数无效时返回原始代码
     */
    private String extractPureBondCode(String bondCode, HtMarketEnum market) {
        if (StrUtil.isBlank(bondCode) || market == null) {
            return bondCode;
        }

        // 移除市场后缀
        String suffix = market.getSuffix();
        if (StrUtil.isNotBlank(suffix) && bondCode.contains(suffix)) {
            return bondCode.replace(suffix, "");
        }

        return bondCode;
    }

    /**
     * 判断手动入库债券是否在有效期内
     *
     * <p>
     * 有效期判断规则：当前日期 >= 入库时间 且 当前日期 <= 终止时间
     * </p>
     * <p>
     * 任一时间为null或债券对象为null时，视为无效
     * </p>
     *
     * @param manualBond 手动入库债券对象
     * @return boolean true表示在有效期内，false表示已过期或无效
     */
    private boolean isValidManualBond(ManualBondEntryDO manualBond) {
        if (manualBond == null) {
            return false;
        }

        Date currentDate = new Date();
        Date entryTime = manualBond.getEntryTime();
        Date endTime = manualBond.getEndTime();

        // 检查入库时间和终止时间是否为空
        if (entryTime == null || endTime == null) {
            LogTool.warn("手动入库债券[{}]的时间配置不完整：entryTime={}, endTime={}", manualBond.getBondCode(),
                    entryTime, endTime);
            return false;
        }

        // 将日期转换为int类型进行比较（格式：yyyyMMdd）
        int currentDateInt = Integer.parseInt(
                DateUtil.formateDate(currentDate, DateUtil.EnumDateTimeFormat.DATENumeric8.getFormat()));
        int entryTimeInt = Integer.parseInt(
                DateUtil.formateDate(entryTime, DateUtil.EnumDateTimeFormat.DATENumeric8.getFormat()));
        int endTimeInt = Integer.parseInt(
                DateUtil.formateDate(endTime, DateUtil.EnumDateTimeFormat.DATENumeric8.getFormat()));

        // 判断当前日期是否在有效期内：entryTime <= currentDate <= endTime
        return currentDateInt >= entryTimeInt && currentDateInt <= endTimeInt;
    }

    /**
     * 构建基础响应对象
     *
     * <p>
     * 根据债券类型选择数据源构建可投额度查询的基础响应对象。
     * </p>
     * <p>
     * 数据源选择策略：
     * </p>
     * <ul>
     * <li>境外债券：从 offshoreBondPositionDO 字段获取基础信息</li>
     * <li>境内债券：从 bondBaseInfoRespDTO 字段获取基础信息</li>
     * </ul>
     *
     * @param bondInfoDataBO    债券基本信息
     * @param eligibilityResult
     * @return InvestableQuotaQueryVO 基础响应对象
     */
    private InvestableQuotaQueryVO buildBaseResponse(BondInfoDataBO bondInfoDataBO,
                                                     InvestableResultBO eligibilityResult) {
        InvestableQuotaQueryVO result = new InvestableQuotaQueryVO();

        if (bondInfoDataBO.isOffshore()) {
            // 境外债券：从 offshoreBondPositionDO 获取信息
            OffshoreBondPositionDO offshoreBond = bondInfoDataBO.getOffshoreBondPositionDO();
            if (Objects.nonNull(offshoreBond)) {
                result.setBondCode(offshoreBond.getBondMarketCode());
                result.setBondName(offshoreBond.getBondName());
                result.setBondType(offshoreBond.getBondType());
                result.setManCompany(offshoreBond.getMainCompany());
                result.setRegion(offshoreBond.getRegion());
                result.setUnderwriter(null); // 境外债券通常没有担保人信息
                result.setYyManCompanyType(null); // 境外债券没有主体类型分类
                result.setPerpetualBond(null); // 境外债券的永续债标识需要从其他字段判断
                result.setStrIsMdebt(null); // 境外债券的次级债标识需要从bondType判断
            }
        } else {
            // 境内债券：从 bondBaseInfoRespDTO 获取信息
            BondBaseInfoRespDTO bondInfo = bondInfoDataBO.getBondBaseInfoRespDTO();
            if (Objects.nonNull(bondInfo)) {
                result.setBondCode(bondInfo.getSecuCode());
                result.setBondName(bondInfo.getBondNameAbbr());
                result.setBondType(bondInfo.getStrDczcYjfl());
                result.setInstName(bondInfo.getInstName());
                result.setUnderwriter(bondInfo.getWtyCompname());
                result.setYyManCompanyType(bondInfo.getYySubjectType());
                result.setPerpetualBond(bondInfo.getPerpetualBond());
                result.setStrIsMdebt(bondInfo.getStrIsMdebt());
                result.setManCompany(Opt.ofNullable(eligibilityResult)
                        .map(InvestableResultBO::getManCompanyCreditBondReportVO)
                        .map(ManCompanyCreditBondReportVO::getCompany)
                        .get());
                result.setManCompanyEvaluation(Opt.ofNullable(eligibilityResult)
                        .map(InvestableResultBO::getManCompanyCreditBondReportVO)
                        .map(ManCompanyCreditBondReportVO::getRiskInternalEvaluation)
                        .get());
                result.setYyaArea(bondInfo.getYyaArea());
                result.setYyaAdminLevel(bondInfo.getYyaAdminLevel());
            }
        }

        // 剩余期限可以通过BondInfoDataBO的方法直接获取
        result.setRemTermYear(bondInfoDataBO.getRemainingTermNum());

        return result;
    }

    /**
     * 判断是否为利率债或可转债
     *
     * <p>
     * 根据Choice东财债券分类判断债券类型：
     * </p>
     * <ul>
     * <li>利率债：基于一级分类判断</li>
     * <li>可转债：基于二级分类判断</li>
     * </ul>
     * <p>
     * 这两类债券不适用于信用债额度管理
     * </p>
     *
     * @param primaryClassification   东财债券一级分类
     * @param secondaryClassification 东财债券二级分类
     * @return boolean true表示是利率债或可转债，false表示是其他类型债券
     */
    private boolean isInterestRateBondOrConvertibleBond(String primaryClassification, String secondaryClassification) {
        // 利率债判断（基于东财债券一级分类）
        if (StrUtil.equals(primaryClassification, ChoiceBondTypeEnum.INRB.getName())) {
            return true;
        }

        // 可转债判断（基于东财债券二级分类）
        if (StrUtil.equals(secondaryClassification, EJFL_KZZ)) {
            return true;
        }
        return false;
    }

    /**
     * 通过公司名称查询内评信息
     *
     * <p>
     * 从主体信用债券报告基础信息表中查询公司的内评等级信息。
     * </p>
     * <p>
     * 如果查询到多条记录，返回第一条；如果公司名称为空，返回null。
     * </p>
     *
     * @param companyName 公司名称，不能为空
     * @return ManCompanyCreditBondReportVO 公司内评信息，查询失败或公司名称为空时返回null
     */
    private ManCompanyCreditBondReportVO getManCompanyCreditBond(String companyName) {
        if (StrUtil.isBlank(companyName)) {
            return null;
        }
        ManCompanyCreditBondReportREQ req = new ManCompanyCreditBondReportREQ();
        req.setCompany(companyName);
        req.setPageNo(1);
        req.setPageSize(Integer.MAX_VALUE);
        ApiResponse<ManCompanyCreditBondReportVO> manCompanyCreditBondReportVOApiResponse = creditBondReportService.manCompanyQueryPage(
                req);


        return Opt.ofNullable(manCompanyCreditBondReportVOApiResponse)
                .map(ApiResponse::getData)
                .map(list -> CollUtil.getFirst(list))
                .get();
    }

    /**
     * 验证债券入库规则，返回不符合条件的错误原因
     *
     * <p>
     * 根据发行人内评等级和债券特征验证是否符合入库规则：
     * </p>
     * <ul>
     * <li>内评1-3级：同时为次级+永续债不可入库，单独的次级债或永续债可入库</li>
     * <li>内评4-5级：永续债不可入库</li>
     * <li>内评6级：含利率跳升机制或回售条款且行权剩余期限>3年不可入库</li>
     * <li>内评7级及以上：不可入库</li>
     * <li>城投债：内评4级及以上不可入库</li>
     * </ul>
     *
     * @param instNameLevel    发行人内评等级（1-10，数字越小等级越高）
     * @param isPerpetual      是否永续债
     * @param isMdebt          是否次级债
     * @param remainingTermNum 剩余期限（年）
     * @param interestRateJump 是否含利率跳升机制
     * @param hasPutOption     是否含回售条款
     * @param ctz              是否城投债
     * @return String 错误提示信息，null或空字符串表示验证通过
     */
    public String validateZyRules(int instNameLevel, boolean isPerpetual, boolean isMdebt,
                                  BigDecimal remainingTermNum, boolean interestRateJump,
                                  boolean hasPutOption, boolean ctz) {

        // 发行人内评1-3级时，同时为次级、永续的债券不可入库，次级债或永续债可以入库。
        if (instNameLevel >= 1 && instNameLevel <= 3) {
            if (isPerpetual && isMdebt) {
                return "发行人内评1-3级，同时为次级、永续的债券不可入库";
            }
        } else if (instNameLevel >= 4 && instNameLevel <= 5) {
            // 发行人内评4-5级时，次级债、永续债都不可入库。
            if (isPerpetual) {
                return "发行人内评4-5级，为永续债，不可入库";
            }
            if (isMdebt) {
                return "发行人内评4-5级，为次级债，不可入库";
            }
        } else if (instNameLevel == 6) {
            // 发行人内评6的永续债，当该债券由城投主体发行、行权剩余期限在3年以内、含利率跳升机制保护或含回售条款、非次级债，也可入库
            if (isPerpetual) {
                if (isMdebt) {
                    return "发行人内评6，为次级债，不可入库";
                }
                // 主体类型："城投"或为空，则符合条件
                if (!ctz) {
                    return "发行人内评6，非城投主体，不可入库";
                }
                // 查询剩余期限配置
                ApiResponse<ExerciseRemainingTermLimitVO> exerciseRemainingTermLimit = queryExerciseRemainingTermLimit();
                BigDecimal limit = Opt.ofNullable(exerciseRemainingTermLimit)
                        .map(ApiResponse::getData)
                        .map(CollUtil::getFirst)
                        .map(ExerciseRemainingTermLimitVO::getTermLimit)
                        .get();
                if (Objects.nonNull(remainingTermNum) && Objects.nonNull(limit) && remainingTermNum.compareTo(limit) > 0) {
                    return "发行人内评6，行权剩余期限超过" + limit + "年，不可入库";
                }
                // 是否包括含利率跳升机制保护条款
                if (!interestRateJump && !hasPutOption) {
                    return "发行人内评6，不含利率跳升机制保护条款和回售条款，不可入库";
                }
            }
        } else if (instNameLevel == 7) {
            // 发行人内评7的债券，只有含权普通债（非次级债非永续债）可以入库
            if (isPerpetual) {
                return "发行人内评7级，为永续债，不可入库";
            }
            if (isMdebt) {
                return "发行人内评7级，为次级债，不可入库";
            }
        } else if (instNameLevel > 7) {
            // 发行人内评大于7的债券，不可入库。
            return "发行人内评大于7级，不可入库";
        }

        // 验证通过
        return null;
    }

    /**
     * 验证债券入库规则，返回不符合条件的错误原因
     *
     * <p>
     * 根据发行人内评等级和债券特征验证是否符合入库规则：
     * </p>
     * <ul>
     * <li>内评1-3级：同时为次级+永续债不可入库，单独的次级债或永续债可入库</li>
     * <li>内评4-5级：永续债不可入库</li>
     * <li>内评6级：含利率跳升机制或回售条款且行权剩余期限>3年不可入库</li>
     * <li>内评7级及以上：不可入库</li>
     * <li>城投债：内评4级及以上不可入库</li>
     * </ul>
     *
     * @param instNameLevel    发行人内评等级（1-10，数字越小等级越高）
     * @param isPerpetual      是否永续债
     * @param isMdebt          是否次级债
     * @param remainingTermNum 剩余期限（年）
     * @param interestRateJump 是否含利率跳升机制
     * @param hasPutOption     是否含回售条款
     * @param ctz              是否城投债
     * @return String 错误提示信息，null或空字符串表示验证通过
     */
    public String validateMarketMakingRules(int instNameLevel, boolean isPerpetual, boolean isMdebt,
                                  BigDecimal remainingTermNum, boolean interestRateJump,
                                  boolean hasPutOption, boolean ctz) {

        // 发行人内评1-3级时可以入库。
        if (instNameLevel >= 1 && instNameLevel <= 3) {
            return null;
        } else if (instNameLevel >= 4 && instNameLevel <= 5) {
            // 发行人内评4-5级时，次级债、永续债都不可入库。
            if (isPerpetual) {
                return "发行人内评4-5级，为永续债，不可入库";
            }
            if (isMdebt) {
                return "发行人内评4-5级，为次级债，不可入库";
            }
        } else  {
            return "发行人内评大于5级，不可入库";
        }

       return null;
    }
    // ==================== 行权剩余期限限制配置相关方法 ====================

    /**
     * 通用配置参数查询方法
     *
     * @param bizType   业务类型
     * @param paramName 参数名称
     * @return 参数值，未配置时返回null
     */
    private String getParamConfigValue(String bizType, String paramName) {
        List<ReportParamConfigDO> configDOS = ReportParamConfigDao.Instance.getByBizType(bizType);

        for (ReportParamConfigDO configDO : configDOS) {
            if (paramName.equals(configDO.getParamName())) {
                return configDO.getParamValue();
            }
        }

        return null;
    }

    /**
     * 通用配置参数查询方法（返回BigDecimal类型）
     *
     * @param bizType   业务类型
     * @param paramName 参数名称
     * @return 参数值转换为BigDecimal，未配置或格式错误时返回null
     */
    private BigDecimal getParamConfigValueAsDecimal(String bizType, String paramName) {
        String paramValue = getParamConfigValue(bizType, paramName);

        if (StrUtil.isNotBlank(paramValue)) {
            BigDecimal decimalValue = BigDecimalUtil.tryParseDecimal(paramValue);
            if (Objects.nonNull(decimalValue)) {
                return decimalValue;
            } else {
                LogTool.warn("配置参数值格式错误: bizType={}, paramName={}, paramValue={}", bizType, paramName,
                        paramValue);
            }
        }

        return null;
    }

    /**
     * 查询集中度限额配置
     *
     * @return 集中度限额配置值，未配置时返回null
     */
    private BigDecimal queryConcentrationLimitConfig() {
        return getParamConfigValueAsDecimal(CONCENTRATION_LIMIT_BIZ_TYPE, CONCENTRATION_LIMIT_PARAM_NAME);
    }

    /**
     * 查询区域限额
     *
     * @param region 区域名称
     * @return 区域限额，未配置时返回null
     */
    private BigDecimal queryRegionLimit(String region) {
        if (StrUtil.isBlank(region)) {
            return null;
        }

        try {
            List<ReportCreditNewRegionLimitManagementBO> regionLimits = ReportCreditNewRegionLimitManagementDao.Instance.queryPage(
                    region);
            if (CollUtil.isNotEmpty(regionLimits)) {
                ReportCreditNewRegionLimitManagementBO regionLimit = regionLimits.get(0);
                //数据库单位为亿元，转换成万元
                if (Objects.nonNull(regionLimit.getRegionLimit())) {
                    return BigDecimalUtil.multiply(regionLimit.getRegionLimit(),
                            NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum());
                }
                return null;
            }
        } catch (Exception e) {
            LogTool.warn("查询区域限额失败: region={}, error={}", region, e.getMessage());
        }

        return null;
    }

    /**
     * 查询行权剩余期限限制配置
     *
     * @return 行权剩余期限限制配置信息
     */
    public ApiResponse<ExerciseRemainingTermLimitVO> queryExerciseRemainingTermLimit() {
        ExerciseRemainingTermLimitVO limitVO = new ExerciseRemainingTermLimitVO();

        String paramValue = getParamConfigValue(EXERCISE_REMAINING_TERM_LIMIT_BIZ_TYPE,
                EXERCISE_REMAINING_TERM_LIMIT_PARAM_NAME);
        if (StrUtil.isNotBlank(paramValue)) {
            BigDecimal limitValue = BigDecimalUtil.tryParseDecimal(paramValue);
            if (Objects.nonNull(limitValue)) {
                limitVO.setTermLimit(limitValue);
            }
        } else {
            // 空值时设置为null，表示未配置
            limitVO.setTermLimit(null);
        }

        return ApiResponse.success(limitVO);
    }

    /**
     * 保存行权剩余期限限制配置
     *
     * @param vo 行权剩余期限限制配置信息
     * @return 保存结果
     */
    public ApiResponse<Object> saveExerciseRemainingTermLimit(ExerciseRemainingTermLimitVO vo) {
        DAssert.assertFalse(Objects.isNull(vo), FICC_ORDER_BOND_COMMON_ERROR.fillMsg("配置信息不能为空"));

        String paramValue = null;
        if (Objects.nonNull(vo.getTermLimit())) {
            // 验证数值有效性
            BigDecimal limitValue = vo.getTermLimit();
            DAssert.assertTrue(limitValue.compareTo(BigDecimal.ZERO) >= 0,
                    FICC_ORDER_BOND_COMMON_ERROR.fillMsg("行权剩余期限限制必须大于等于0"));
            paramValue = limitValue.toPlainString();
        }
        // null值时paramValue保持为null，表示清除配置

        ReportParamConfigDao.Instance.saveAndUpdate(RandomUtil.randomUUID(), EXERCISE_REMAINING_TERM_LIMIT_BIZ_TYPE,
                EXERCISE_REMAINING_TERM_LIMIT_PARAM_NAME, paramValue);

        return new ApiResponse<>().setOk();
    }

    /**
     * 验证请求参数
     *
     * @param bondCode 债券代码
     * @param dayNum   查询天数
     */
    private void validateRequestParameters(String bondCode, Integer dayNum) {
        DAssert.assertTrue(CharSequenceUtil.isNotBlank(bondCode),
                FICC_ORDER_BOND_COMMON_ERROR.fillMsg("债券代码不能为空"));
        DAssert.assertTrue(Objects.nonNull(dayNum) && dayNum >= 1 && dayNum <= 7,
                FICC_ORDER_BOND_COMMON_ERROR.fillMsg("查询天数范围为1-7"));
    }

    /**
     * 计算债券余额（市场流通量）
     *
     * @param traceId
     * @param bondInfo 债券基本信息
     * @return BigDecimal 债券余额（万元）
     */
    private BigDecimal calculateBondBalance(String traceId, BondInfoDataBO bondInfo) {
        String string = Opt.ofNullable(bondInfo)
                .map(BondInfoDataBO::getBondFxIndicatoraRespDTO)
                // Chocie单位为元，转换为万元
                .map(BondFxIndicatoraRespDTO::getBondBalance)
                .get();
        if (StrUtil.isBlank(string)){
            LogTool.info("traceId:{},债券余额为空: 估值信息={}",traceId, bondInfo.getBondFxIndicatoraRespDTO());
        }
        return  Opt.ofNullable(string)
                .map(a -> BigDecimalUtil.divide(BigDecimalUtil.tryParseDecimal(a),
                        NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum(), 0))
                .get();
    }

    /**
     * 计算各组别可投额度
     *
     * @param bondMarketCode                  债券代码
     * @param bondInfo                  债券基本信息
     * @param creditInfo                信用信息
     * @param hasZyPermission           是否有自营权限
     * @param hasMarketMakingPermission 是否有做市权限
     * @param zyAccountSet              自营账户集合
     * @param marketMakingAccountSet    做市账户集合
     * @param companyRatingMap          公司内评等级映射
     * @param manualBonds               手动入库债券列表
     * @param quotaConfig               占用额度配置
     * @param t0AmountFunction          T+0金额提取函数
     * @param tNAmountFunction          T+N金额提取函数
     * @param i7Quota                   内评7级限额
     * @param i6Quota                   内评6级限额
     * @param bondLimit                 债券集中度限额
     * @return List<InvestableQuotaQueryVO> 各组别可投额度结果列表
     */
    private List<InvestableQuotaQueryVO> calculateGroupQuotas(String bondMarketCode, BondInfoDataBO bondInfo,
                                                              ManCompanyCreditBondReportVO creditInfo,
                                                              boolean hasZyPermission,
                                                              boolean hasMarketMakingPermission,
                                                              Set<String> zyAccountSet,
                                                              Set<String> marketMakingAccountSet,
                                                              Map<String, Integer> companyRatingMap,
                                                              List<ManualBondEntryDO> manualBonds,
                                                              OccupancyQuotaVO quotaConfig,
                                                              Function<PositionDataVO, String> t0AmountFunction,
                                                              Function<PositionDataVO, String> tNAmountFunction,
                                                              BigDecimal i7Quota, BigDecimal i6Quota,
                                                              BigDecimal bondLimit,
                                                              InvestableResultBO eligibilityResult) {

        List<InvestableQuotaQueryVO> results = new ArrayList<>();

        // 计算自营组别额度
        if (hasZyPermission) {
            InvestableQuotaQueryVO zyResult = getQuotaResult(bondMarketCode, bondInfo, creditInfo, zyAccountSet,
                    companyRatingMap, manualBonds, quotaConfig, t0AmountFunction, tNAmountFunction,
                    i7Quota, i6Quota, bondLimit, ZY_GROUP,
                    Opt.ofNullable(creditInfo)
                            .map(ManCompanyCreditBondReportVO::getPtQuota)
                            .get(),
                    eligibilityResult);
            results.add(zyResult);
        }

        // 计算做市组别额度
        if (hasMarketMakingPermission) {
            InvestableQuotaQueryVO marketMakingResult = getQuotaResult(bondMarketCode, bondInfo, creditInfo,
                    marketMakingAccountSet, companyRatingMap, manualBonds, quotaConfig, t0AmountFunction,
                    tNAmountFunction, i7Quota, i6Quota, bondLimit, MARKET_MAKING_GROUP,
                    Opt.ofNullable(creditInfo)
                            .map(ManCompanyCreditBondReportVO::getPmQuota)
                            .get(),
                    eligibilityResult);
            results.add(marketMakingResult);
        }

        return results;
    }

    /**
     * 检查用户权限
     *
     * @return UserPermissionResult 用户权限检查结果
     */
    private UserPermissionResult checkUserPermissions() {
        // 查询报告配置
        Map<String, Object> reportConfigParam = Maps.newHashMap();
        List<ReportConfigDO> configDOS = ReportConfigDao.Instance.selectPage(reportConfigParam);

        // 构建账户集合
        Set<String> zyAccountSet = configDOS.stream()
                .filter(x -> !CollUtil.contains(ZY_NOT_IN_GROUP_NAMES, x.getGroupName()))
                .map(ReportConfigDO::getInsideSecAccName)
                .filter(StrUtil::isNotEmpty)
                .collect(Collectors.toSet());

        Set<String> marketMakingAccountSet = configDOS.stream()
                .filter(x -> MARKETMAKING_GROUP_NAME.equals(x.getGroupName()))
                .map(ReportConfigDO::getInsideSecAccName)
                .filter(StrUtil::isNotEmpty)
                .collect(Collectors.toSet());

        // 获取用户权限账户
        List<String> userAccountList = accountTreeService.getAuthorityName(UserSessionUtil.getUser()
                .getUserName());

        // 判断权限
        boolean hasZyPermission = zyAccountSet.stream()
                .anyMatch(userAccountList::contains);
        boolean hasMarketMakingPermission = marketMakingAccountSet.stream()
                .anyMatch(userAccountList::contains);

        return new UserPermissionResult(hasZyPermission, hasMarketMakingPermission, zyAccountSet,
                marketMakingAccountSet);
    }

    /**
     * 准备查询所需的基础数据
     *
     * @param dayNum         查询天数
     * @param bondBalanceNum 债券余额
     * @return QuotaQueryData 查询数据对象
     */
    private QuotaQueryData prepareQuotaQueryData(Integer dayNum, BigDecimal bondBalanceNum) {
        // 查询公司内评等级映射
        List<ManCompanyCreditBondReportVO> companyReports = ManCompanyCreditBondReportBaseInfoDao.Instance.queryPage(
                null, null);
        Map<String, Integer> companyRatingMap = companyReports.stream()
                .filter(x -> StrUtil.isNotEmpty(x.getCompany()) && ObjectUtil.isNotNull(x.getRiskInternalEvaluation()))
                .collect(Collectors.toMap(ManCompanyCreditBondReportVO::getCompany,
                        ManCompanyCreditBondReportVO::getRiskInternalEvaluation, (oldVal, newVal) -> newVal));

        // 查询手动入库债券
        List<ManualBondEntryDO> manualBonds = ManualBondEntryDao.Instance.queryPage(new HashMap<>());

        // 获取占用额度配置
        OccupancyQuotaVO quotaConfig = getOccupancyQuotaVO();


        // 构建金额提取函数
        Function<PositionDataVO, String> t0AmountFunction = PositionDataVO::getTActualTrustee;
        Function<PositionDataVO, String> tnAmountFunction = x -> {
            TrusteeTnVO trusteeTnVO = x.getTrusteeTnVO();
            Object tAmount = ReflectUtil.getFieldValue(trusteeTnVO, "t" + dayNum + "TrusteeAmount");
            return tAmount == null ? null : String.valueOf(tAmount);
        };

        // 查询特殊内评限额配置
        BigDecimal i7Quota = null;
        BigDecimal i6Quota = null;
        ApiResponse<SpecialInsideScaleLimitMaintainVO> specialLimitResponse = reportCreditCouponsLineManagementService.querySpecialInsideScaleLimitMaintain();
        SpecialInsideScaleLimitMaintainVO specialLimit = Opt.ofNullable(specialLimitResponse)
                .map(ApiResponse::getData)
                .map(CollUtil::getFirst)
                .get();
        if (Objects.nonNull(specialLimit)) {
            // 万元
            i7Quota = BigDecimalUtil.tryParseDecimal(specialLimit.getBelowSeven());
            i6Quota = BigDecimalUtil.tryParseDecimal(specialLimit.getBelowSix());
        }

        // 计算债券集中度限额，0.15，存在数据库为15，需要除以100
        BigDecimal occupancyQuotaLimitConfig = queryConcentrationLimitConfig();
        BigDecimal bondLimit = null;
        if (Objects.nonNull(bondBalanceNum) && Objects.nonNull(occupancyQuotaLimitConfig)) {
            // 集中度限额 = 债券余额 × 集中度限额配置比例
            bondLimit = BigDecimalUtil.multiply(bondBalanceNum,
                    BigDecimalUtil.divide(occupancyQuotaLimitConfig, new BigDecimal("100"), 2));
        }

        return new QuotaQueryData(companyRatingMap, manualBonds, quotaConfig, t0AmountFunction,
                tnAmountFunction, i7Quota, i6Quota, bondLimit);
    }

    /**
     * 查询数据封装类
     */
    private static class QuotaQueryData {
        private final Map<String, Integer> companyRatingMap;
        private final List<ManualBondEntryDO> manualBonds;
        private final OccupancyQuotaVO quotaConfig;
        private final Function<PositionDataVO, String> t0AmountFunction;
        private final Function<PositionDataVO, String> tnAmountFunction;
        private final BigDecimal i7Quota;
        private final BigDecimal i6Quota;
        private final BigDecimal bondLimit;

        public QuotaQueryData(Map<String, Integer> companyRatingMap, List<ManualBondEntryDO> manualBonds,
                              OccupancyQuotaVO quotaConfig,
                              Function<PositionDataVO, String> t0AmountFunction,
                              Function<PositionDataVO, String> tnAmountFunction, BigDecimal i7Quota, BigDecimal i6Quota,
                              BigDecimal bondLimit) {
            this.companyRatingMap = companyRatingMap;
            this.manualBonds = manualBonds;
            this.quotaConfig = quotaConfig;
            this.t0AmountFunction = t0AmountFunction;
            this.tnAmountFunction = tnAmountFunction;
            this.i7Quota = i7Quota;
            this.i6Quota = i6Quota;
            this.bondLimit = bondLimit;
        }

        public Map<String, Integer> getCompanyRatingMap() {
            return companyRatingMap;
        }

        public List<ManualBondEntryDO> getManualBonds() {
            return manualBonds;
        }

        public OccupancyQuotaVO getQuotaConfig() {
            return quotaConfig;
        }


        public Function<PositionDataVO, String> getT0AmountFunction() {
            return t0AmountFunction;
        }

        public Function<PositionDataVO, String> getTnAmountFunction() {
            return tnAmountFunction;
        }

        public BigDecimal getI7Quota() {
            return i7Quota;
        }

        public BigDecimal getI6Quota() {
            return i6Quota;
        }

        public BigDecimal getBondLimit() {
            return bondLimit;
        }
    }

    /**
     * 用户权限检查结果
     */
    private static class UserPermissionResult {
        private final boolean hasZyPermission;
        private final boolean hasMarketMakingPermission;
        private final Set<String> zyAccountSet;
        private final Set<String> marketMakingAccountSet;

        public UserPermissionResult(boolean hasZyPermission, boolean hasMarketMakingPermission,
                                    Set<String> zyAccountSet, Set<String> marketMakingAccountSet) {
            this.hasZyPermission = hasZyPermission;
            this.hasMarketMakingPermission = hasMarketMakingPermission;
            this.zyAccountSet = zyAccountSet;
            this.marketMakingAccountSet = marketMakingAccountSet;
        }

        public boolean hasAnyPermission() {
            return hasZyPermission || hasMarketMakingPermission;
        }

        public boolean hasZyPermission() {
            return hasZyPermission;
        }

        public boolean hasMarketMakingPermission() {
            return hasMarketMakingPermission;
        }

        public Set<String> getZyAccountSet() {
            return zyAccountSet;
        }

        public Set<String> getMarketMakingAccountSet() {
            return marketMakingAccountSet;
        }
    }

}
