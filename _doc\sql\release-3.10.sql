ALTER TABLE ptms.ficc_offshore_bond_position ADD isItACityInvestment int  default 0 COMMENT '是否城投债(0 否/1 是)';
ALTER TABLE ptms.ficc_offshore_bond_position ADD region varchar(256) NULL COMMENT '所属区域';
ALTER TABLE ptms.ficc_report_credit_line_management DROP INDEX ficc_report_credit_line_management_bizDate_IDX_UNI;
ALTER TABLE ptms.ficc_report_credit_line_management ADD `belongingGroup` VARCHAR(32) NULL COMMENT '所属组别';

CREATE TABLE `ficc_man_company_credit_bond_report_base_info` (
    `EID` char(32) NOT NULL,
    `EITIME` datetime DEFAULT CURRENT_TIMESTAMP,
    `EUTIME` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `del` int DEFAULT '0' COMMENT '逻辑删除',
    `company` varchar(256) DEFAULT NULL COMMENT '公司',
    `riskInternalEvaluation` int DEFAULT NULL COMMENT '风控内评等级',
    `fixQuota` decimal(24,12) DEFAULT NULL COMMENT '固收额度（亿元）',
    `pmQuota` decimal(24,12) DEFAULT NULL COMMENT '做市额度（亿元）',
    `regNum` varchar(256) DEFAULT NULL COMMENT '主体代码',
    PRIMARY KEY (`EID`),
    UNIQUE KEY `idx_man_company_credit_bond_report_regNum` (`regNum`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='主体内评及授信维护表';

CREATE TABLE `ht_credit_risk_management` (
    `EID` BIGINT NOT NULL AUTO_INCREMENT,
    `EITIME` datetime DEFAULT CURRENT_TIMESTAMP,
    `EUTIME` datetime DEFAULT CURRENT_TIMESTAMP,
    `del` int DEFAULT '0' COMMENT '逻辑删除',
    `O_NAME` varchar(500) DEFAULT NULL COMMENT '客户名称',
    `O_REGNO` varchar(255) DEFAULT NULL COMMENT '统一社会信用代码',
    `AWC_QUOTA` decimal(31,8) DEFAULT NULL COMMENT '授信额度',
    `B_GRADE2` varchar(100) DEFAULT NULL COMMENT '内部评级',
    PRIMARY KEY (`EID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='衡泰信用风险管理';

ALTER TABLE ptms.ficc_bond_valuation_info
    ADD remTermYear varchar(64) NULL COMMENT '剩余期限(行权,年)';

ALTER TABLE ptms.ficc_bond_base_info
    ADD yySubjectType varchar(128) NULL COMMENT '主体类型';
ALTER TABLE ptms.ficc_bond_base_info
    ADD yyaArea varchar(256) NULL COMMENT '所属政府';
ALTER TABLE ptms.ficc_bond_base_info
    ADD yyaAdminLevel varchar(128) NULL COMMENT '行政级别';
ALTER TABLE ptms.ficc_bond_base_info
    ADD strTermstype varchar(512) NULL COMMENT '特殊条款';
ALTER TABLE ptms.ficc_bond_base_info
    ADD rateClausecontent varchar(2048) NULL COMMENT '利率条款文字';

INSERT INTO ficc_report_param_config (EID, EITIME, EUTIME, bizType, paramName, paramValue)
VALUES ('8a03931d29674cba9dd8bbdad5cc752a', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '信用债额度管理-个券额度管理',
        '集中度限额', '15');

CREATE TABLE `ficc_report_credit_new_coupons_line_management`
(
    `EID`                   char(32)     NOT NULL,
    `EITIME`                datetime        DEFAULT CURRENT_TIMESTAMP,
    `EUTIME`                datetime        DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `bizDate`               int          NOT NULL COMMENT '业务日期',
    `bondMarketCode`        varchar(32)  NOT NULL COMMENT '债券代码带后缀',
    `bondCode`              varchar(32)     DEFAULT NULL COMMENT '债券代码',
    `bondName`              varchar(256)    DEFAULT NULL COMMENT '债券简称',
    `market`                varchar(32)  NOT NULL COMMENT '交易市场',
    `insideSecAccName`      varchar(128)    DEFAULT NULL COMMENT '内部证券账户',
    `insideSecAccNo`        varchar(128) NOT NULL COMMENT '内部证券账户代码',
    `instname`              varchar(256)    DEFAULT NULL COMMENT '发行人名称',
    `underwriter`           varchar(256)    DEFAULT NULL COMMENT '担保人',
    `groupName`             varchar(128)    DEFAULT NULL COMMENT '组别',
    `bondSecondType`        varchar(32)     DEFAULT NULL COMMENT '东财债券二级分类2021',
    `strIsmdebt`            varchar(32)     DEFAULT NULL COMMENT '是否次级债',
    `perpetualbond`         varchar(32)     DEFAULT NULL COMMENT '是否永续债',
    `yyManCompanyType`      varchar(128)    DEFAULT NULL COMMENT '主体类型',
    `yyGovernment`          varchar(128)    DEFAULT NULL COMMENT '所属政府',
    `yyAdminLevel`          varchar(128)    DEFAULT NULL COMMENT '行政级别',
    `remTerm`               varchar(128)    DEFAULT NULL COMMENT '特殊剩余期限',
    `remTermYear`           varchar(128)    DEFAULT NULL COMMENT '剩余期限(行权,年)',
    `instnameEvaluation`    int             DEFAULT NULL COMMENT '发行人内评',
    `underwriterEvaluation` int             DEFAULT NULL COMMENT '担保人内评',
    `manCompany`            varchar(256)    DEFAULT NULL COMMENT '所属主体',
    `manCompanyEvaluation`  int             DEFAULT NULL COMMENT '主体内评',
    `region`                varchar(256)    DEFAULT NULL COMMENT '所属区域',
    `regionEvaluation`      int             DEFAULT NULL COMMENT '区域内评',
    `manCompanyLimit`       decimal(28, 12) DEFAULT NULL COMMENT '主体限额(亿元)',
    `regionLimit`           decimal(28, 12) DEFAULT NULL COMMENT '区域限额(亿元)，-1表示不限',
    `quotaFactor`           varchar(128)    DEFAULT NULL COMMENT '额度折算系数',
    `positionAmountT0`      decimal(28, 6)  DEFAULT NULL COMMENT 'T+0持仓面额(万元)',
    `occupationQuotaT0`     decimal(28, 6)  DEFAULT NULL COMMENT 'T+0占用额度(万元)',
    `availableAmountT0`     decimal(28, 6)  DEFAULT NULL COMMENT 'T+0可用面额(万元)',
    `highLight`             int             DEFAULT 0 comment '高亮标识 1-高亮 0-正常',
    PRIMARY KEY (`EID`),
    UNIQUE KEY `idx_bizdate_bond_market_code_inside_sec_acc_no` (`bizDate`,`bondMarketCode`,`insideSecAccNo`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='信用债个券额度管理新表';


CREATE TABLE `ficc_report_credit_new_coupons_line_management_tn`
(
    `EID`               char(32)     NOT NULL,
    `EITIME`            datetime       DEFAULT CURRENT_TIMESTAMP,
    `EUTIME`            datetime       DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `bizDate`           int          NOT NULL COMMENT '业务日期',
    `bondMarketCode`    varchar(32)  NOT NULL COMMENT '债券代码带后缀',
    `insideSecAccNo`    varchar(128) NOT NULL COMMENT '内部证券账户代码',
    `positionAmountT1`  decimal(28, 6) DEFAULT NULL COMMENT 'T+1持仓面额(万元)',
    `positionAmountT2`  decimal(28, 6) DEFAULT NULL COMMENT 'T+2持仓面额(万元)',
    `positionAmountT3`  decimal(28, 6) DEFAULT NULL COMMENT 'T+3持仓面额(万元)',
    `positionAmountT4`  decimal(28, 6) DEFAULT NULL COMMENT 'T+4持仓面额(万元)',
    `positionAmountT5`  decimal(28, 6) DEFAULT NULL COMMENT 'T+5持仓面额(万元)',
    `positionAmountT6`  decimal(28, 6) DEFAULT NULL COMMENT 'T+6持仓面额(万元)',
    `positionAmountT7`  decimal(28, 6) DEFAULT NULL COMMENT 'T+7持仓面额(万元)',
    `occupationQuotaT1` decimal(28, 6) DEFAULT NULL COMMENT 'T+1占用额度(万元)',
    `occupationQuotaT2` decimal(28, 6) DEFAULT NULL COMMENT 'T+2占用额度(万元)',
    `occupationQuotaT3` decimal(28, 6) DEFAULT NULL COMMENT 'T+3占用额度(万元)',
    `occupationQuotaT4` decimal(28, 6) DEFAULT NULL COMMENT 'T+4占用额度(万元)',
    `occupationQuotaT5` decimal(28, 6) DEFAULT NULL COMMENT 'T+5占用额度(万元)',
    `occupationQuotaT6` decimal(28, 6) DEFAULT NULL COMMENT 'T+6占用额度(万元)',
    `occupationQuotaT7` decimal(28, 6) DEFAULT NULL COMMENT 'T+7占用额度(万元)',
    `availableAmountT1` decimal(28, 6) DEFAULT NULL COMMENT 'T+1可用面额(万元)',
    `availableAmountT2` decimal(28, 6) DEFAULT NULL COMMENT 'T+2可用面额(万元)',
    `availableAmountT3` decimal(28, 6) DEFAULT NULL COMMENT 'T+3可用面额(万元)',
    `availableAmountT4` decimal(28, 6) DEFAULT NULL COMMENT 'T+4可用面额(万元)',
    `availableAmountT5` decimal(28, 6) DEFAULT NULL COMMENT 'T+5可用面额(万元)',
    `availableAmountT6` decimal(28, 6) DEFAULT NULL COMMENT 'T+6可用面额(万元)',
    `availableAmountT7` decimal(28, 6) DEFAULT NULL COMMENT 'T+7可用面额(万元)',
    PRIMARY KEY (`EID`),
    UNIQUE KEY `idx_bond_market_code_inside_sec_acc_no` (`bondMarketCode`,`insideSecAccNo`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='信用债个券额度管理T+N表';


CREATE TABLE `ficc_report_credit_new_region_management`
(
    `EID`               char(32) NOT NULL,
    `EITIME`            datetime        DEFAULT CURRENT_TIMESTAMP,
    `EUTIME`            datetime        DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `bizDate`           int      NOT NULL COMMENT '业务日期',
    `region`            varchar(256)    DEFAULT NULL COMMENT '区域',
    `regionEvaluation`  int             DEFAULT NULL COMMENT '区域内评',
    `regionLimit`       decimal(28, 12) DEFAULT NULL COMMENT '区域限额(亿元)，-1表示不限',
    `positionAmountT0`  decimal(28, 12) DEFAULT NULL COMMENT 'T+0持仓面额(亿元)',
    `positionAmountT1`  decimal(28, 12) DEFAULT NULL COMMENT 'T+1持仓面额(亿元)',
    `positionAmountT2`  decimal(28, 12) DEFAULT NULL COMMENT 'T+2持仓面额(亿元)',
    `positionAmountT3`  decimal(28, 12) DEFAULT NULL COMMENT 'T+3持仓面额(亿元)',
    `positionAmountT4`  decimal(28, 12) DEFAULT NULL COMMENT 'T+4持仓面额(亿元)',
    `positionAmountT5`  decimal(28, 12) DEFAULT NULL COMMENT 'T+5持仓面额(亿元)',
    `positionAmountT6`  decimal(28, 12) DEFAULT NULL COMMENT 'T+6持仓面额(亿元)',
    `positionAmountT7`  decimal(28, 12) DEFAULT NULL COMMENT 'T+7持仓面额(亿元)',
    `occupationQuotaT0` decimal(28, 12) DEFAULT NULL COMMENT 'T+0占用额度(亿元)',
    `occupationQuotaT1` decimal(28, 12) DEFAULT NULL COMMENT 'T+1占用额度(亿元)',
    `occupationQuotaT2` decimal(28, 12) DEFAULT NULL COMMENT 'T+2占用额度(亿元)',
    `occupationQuotaT3` decimal(28, 12) DEFAULT NULL COMMENT 'T+3占用额度(亿元)',
    `occupationQuotaT4` decimal(28, 12) DEFAULT NULL COMMENT 'T+4占用额度(亿元)',
    `occupationQuotaT5` decimal(28, 12) DEFAULT NULL COMMENT 'T+5占用额度(亿元)',
    `occupationQuotaT6` decimal(28, 12) DEFAULT NULL COMMENT 'T+6占用额度(亿元)',
    `occupationQuotaT7` decimal(28, 12) DEFAULT NULL COMMENT 'T+7占用额度(亿元)',
    PRIMARY KEY (`EID`),
    UNIQUE KEY `idx_bizdate_region` (`bizDate`,`region`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='信用债区域额度管理表';


CREATE TABLE `ficc_report_credit_new_region_limit_management`
(
    `EID`              char(32) NOT NULL,
    `EITIME`           datetime        DEFAULT CURRENT_TIMESTAMP,
    `EUTIME`           datetime        DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `region`           varchar(256)    DEFAULT NULL COMMENT '区域',
    `regionEvaluation` int             DEFAULT NULL COMMENT '区域内评',
    `regionLimit`      decimal(28, 12) DEFAULT NULL COMMENT '区域限额（亿元），-1表示不限',
    PRIMARY KEY (`EID`),
    UNIQUE KEY `idx_region` (`region`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='信用债区域内评及授信表';

CREATE TABLE `manual_bond_entry` (
                                     `EID` char(32) NOT NULL COMMENT '主键UUID',
                                     `EITIME` datetime NOT NULL COMMENT '创建时间',
                                     `EUTIME` datetime NOT NULL COMMENT '更新时间',
                                     `del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标志(0:未删除,1:已删除)',
                                     `bondCode` varchar(20) NOT NULL COMMENT '债券代码',
                                     `bondName` varchar(100) NOT NULL COMMENT '债券简称',
                                     `groupType` varchar(10) NOT NULL COMMENT '组别(自营/做市)',
                                     `entryTime` datetime NOT NULL COMMENT '入库时间',
                                     `endTime` datetime NOT NULL COMMENT '终止时间',
                                     PRIMARY KEY (`EID`),
                                     KEY `idx_bondCode` (`bondCode`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='手动入库债券表';

INSERT INTO ficc_report_param_config (EID, EITIME, EUTIME, bizType, paramName, paramValue)
VALUES ('aaa3931d29674cba9dd8bbdad5cc752a', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '信用债额度管理-行权剩余期限限制配置', '行权剩余期限限制', '3');


alter table ficc_convertible_bond_position_detail
    add column fundId int DEFAULT NULL COMMENT '基金编号';

alter table ficc_convertible_bond_position_detail
    add column stockType varchar(32) DEFAULT NULL COMMENT '证券类型，5-可转债 ，F-开放式基金';

alter table ficc_convertible_bond_position_overview
    add column bondMarketValue decimal(48, 8) DEFAULT NULL COMMENT '债券市值（亿）';

alter table ficc_convertible_bond_position_overview
    add column etfMarketValue decimal(48, 8) DEFAULT NULL COMMENT 'etf市值（亿）';