
package com.eastmoney.ptms.data.ficc.BO;

import com.eastmoney.ptms.data.ficc.VO.ManCompanyCreditBondReportVO;

import lombok.Data;

@Data
public class InvestableResultBO {
    /**
     * 是否可准入
     */
    private boolean canInvest;

    private boolean zyCanInvest;
    private boolean marketMakingCanInvest;
    private String zyReason;
    private String marketMakingReason;

    /**
     * 不可准入原因
     */
    private String reason;

    /**
     * 债券信息
     */
    private BondInfoDataBO bondInfoDataBO;

    /**
     * 发行人是所属主体
     */
    private boolean issuerMancompany;
    private ManCompanyCreditBondReportVO manCompanyCreditBondReportVO;

    public static InvestableResultBO notInvestable(String reason) {
        InvestableResultBO result = new InvestableResultBO();
        result.setCanInvest(false);
        result.setReason(reason);
        result.setZyCanInvest(false);
        result.setMarketMakingCanInvest(false);
        result.setZyReason(reason);
        result.setMarketMakingReason(reason);
        return result;
    }

    public static InvestableResultBO canInvestable() {
        InvestableResultBO result = new InvestableResultBO();
        result.setCanInvest(true);
        result.setZyCanInvest(true);
        result.setMarketMakingCanInvest(true);
        return result;
    }

    
}