package com.eastmoney.ptms.ficc.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.eastmoney.digi.common.util.BigDecimalUtil;
import com.eastmoney.digi.common.util.CollectUtil;
import com.eastmoney.digi.common.util.FileUtil;
import com.eastmoney.digi.common.util.RandomUtil;
import com.eastmoney.digi.core.api.ApiResponse;
import com.eastmoney.digi.core.api.ApiUtil;
import com.eastmoney.digi.core.api.SelectCallBack;
import com.eastmoney.digi.core.err.exception.DAssert;
import com.eastmoney.digi.core.redis.lock.RedisLock;
import com.eastmoney.ptms.dao.core.UserSessionUtil;
import com.eastmoney.ptms.data.common.PtmsErrCode;
import com.eastmoney.ptms.data.enums.BooleanEnum;
import com.eastmoney.ptms.data.enums.NumberUnitEnum;
import com.eastmoney.ptms.data.ficc.BO.ArrangeBondDetailBO;
import com.eastmoney.ptms.data.ficc.BO.ReportCreditNewCouponsLineManagementBO;
import com.eastmoney.ptms.data.ficc.DO.OffshoreBondPositionDO;
import com.eastmoney.ptms.data.ficc.DO.ReportConfigDO;
import com.eastmoney.ptms.data.ficc.DO.ReportCreditLineManagementDO;
import com.eastmoney.ptms.data.ficc.DO.TradeAccountPoolDO;
import com.eastmoney.ptms.data.ficc.DTO.choice.BondBaseInfoRespDTO;
import com.eastmoney.ptms.data.ficc.DTO.choice.BondCDBInfoRespDTO;
import com.eastmoney.ptms.data.ficc.REQ.ManCompanyCreditBondReportREQ;
import com.eastmoney.ptms.data.ficc.REQ.OccupancyQuotaVO;
import com.eastmoney.ptms.data.ficc.REQ.PositionDataQueryREQ;
import com.eastmoney.ptms.data.ficc.REQ.ReportCreditLineManagementMainCompanyQueryREQ;
import com.eastmoney.ptms.data.ficc.REQ.ReportCreditLineManagementQueryREQ;
import com.eastmoney.ptms.data.ficc.REQ.ReportCreditNewCouponsLineManagementQueryREQ;
import com.eastmoney.ptms.data.ficc.VO.ArrangeBondDetailVO;
import com.eastmoney.ptms.data.ficc.VO.AvailableTnVO;
import com.eastmoney.ptms.data.ficc.VO.ManCompanyCreditBondReportVO;
import com.eastmoney.ptms.data.ficc.VO.OwnAmountTnVO;
import com.eastmoney.ptms.data.ficc.VO.PositionDataVO;
import com.eastmoney.ptms.data.ficc.VO.ReportCreditLineManagementExcelVO;
import com.eastmoney.ptms.data.ficc.VO.ReportCreditLineManagementVO;
import com.eastmoney.ptms.data.ficc.VO.ReportCreditNewCouponsLineManagementVO;
import com.eastmoney.ptms.ficc.choice.BondChoiceService;
import com.eastmoney.ptms.ficc.choice.IBTradeDateService;
import com.eastmoney.ptms.ficc.dao.DaoTradeAccount;
import com.eastmoney.ptms.ficc.dao.OffshoreBondPositionDao;
import com.eastmoney.ptms.ficc.dao.PositionDataDao;
import com.eastmoney.ptms.ficc.dao.ReportConfigDao;
import com.eastmoney.ptms.ficc.dao.ReportCreditLineManagementDao;
import com.eastmoney.ptms.ficc.dao.ReportCreditNewCouponsLineManagementDao;
import com.eastmoney.ptms.ficc.dao.TradeChangeDetailDao;
import com.eastmoney.ptms.ficc.enums.ChoiceBondTypeEnum;
import com.eastmoney.ptms.ficc.enums.CurrencyEnum;
import com.eastmoney.ptms.ficc.enums.HtMarketEnum;
import com.eastmoney.ptms.ficc.enums.HtOrderStatusEnum;
import com.eastmoney.ptms.ficc.enums.HtTradeDirectionEnum;
import com.eastmoney.ptms.ficc.enums.OffshorePositionBondTypeEnum;
import com.eastmoney.ptms.ficc.enums.TradeChangeDirectionEnum;
import com.eastmoney.ptms.ficc.tool.TimeTool;
import com.eastmoney.ptms.ficc.tool.conversion.ObjConversionTool;
import com.eastmoney.ptms.ficc.tool.log.LogTool;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName ReportCreditLineManagementService
 * @Description
 * @date 2024/2/27
 */
@Service
@RequiredArgsConstructor
public class ReportCreditLineManagementService {

    public static final List<String> OUT_RIGHT_TRADE_DIRECTION = ImmutableList.of(HtTradeDirectionEnum.OUTRIGHT_BUY,HtTradeDirectionEnum.OUTRIGHT_SELL)
            .stream()
            .map(HtTradeDirectionEnum::getCode)
            .collect(Collectors.toList());
    public static final List<String> LEND_TRADE_DIRECTION = ImmutableList.of(HtTradeDirectionEnum.LENDING_BUY,HtTradeDirectionEnum.LENDING_SELL)
            .stream()
            .map(HtTradeDirectionEnum::getCode)
            .collect(Collectors.toList());
    public static final List<String> ZY_DIRECTION = ImmutableList.of(TradeChangeDirectionEnum.质押券冻结,TradeChangeDirectionEnum.质押券解冻)
            .stream()
            .map(TradeChangeDirectionEnum::getCode)
            .collect(Collectors.toList());
    @Autowired
    IBTradeDateService ibTradeDateService;

    private final CreditBondReportService creditBondReportService;

    private final BondChoiceService bondChoiceService;

    private final PositionDataService positionDataService;

    private final AccountTreeService accountTreeService;

    private final static String BUSINESS_NAME = "【信用债额度管理】";

    private final static String MAIN_COMPANY_LOG = "[主体额度计算3.10]";

    private static final BigDecimal calCardinality = new BigDecimal(0.15);

    public static final String OTHER = "OTHER";

    private static final String PTMS = "自营";

    private static final String PMMS = "做市";

    public static Predicate<ReportCreditNewCouponsLineManagementVO> NCDS_FILTER = x -> ChoiceBondTypeEnum.NCDS.getName().equals(x.getRefBondType());

    public static Predicate<ReportCreditNewCouponsLineManagementVO> ABS_OTHER_FILTER = x -> ChoiceBondTypeEnum.ABS.getName().equals(x.getRefBondType())
            || BooleanEnum.TRUE.getName().equals(x.getPerpetualbond())
            || BooleanEnum.TRUE.getName().equals(x.getStrIsmdebt());

    public static final List<String> DAY_END_STATUS_CODE = CollectUtil.asList(HtOrderStatusEnum.CONFIRM.getCode(), HtOrderStatusEnum.SETTLE_SUCCESS.getCode(),
            HtOrderStatusEnum.END_UNSETTLED.getCode(), HtOrderStatusEnum.END_SETTLED.getCode());


    public static final List<String> TRADE_STATUS_LIST = ImmutableList.of(HtOrderStatusEnum.CREATE.getCode(),
            HtOrderStatusEnum.PREAUDIT.getCode(), HtOrderStatusEnum.AUDIT.getCode(), HtOrderStatusEnum.PASS.getCode(),
            HtOrderStatusEnum.EXE.getCode(), HtOrderStatusEnum.CONFIRM.getCode(), HtOrderStatusEnum.SETTLE_SUCCESS.getCode(),
            HtOrderStatusEnum.END_UNSETTLED.getCode(), HtOrderStatusEnum.END_SETTLED.getCode());


    public ApiResponse<ReportCreditLineManagementVO> queryForTotal(ReportCreditLineManagementQueryREQ req) {
        ApiResponse<ReportCreditLineManagementVO> response = new ApiResponse<>();
        Map<String, Object> param = ObjConversionTool.convertMap(req);
        ReportCreditLineManagementDO lineManagementDOS = ReportCreditLineManagementDao.Instance.queryTotal(param);
        if(ObjectUtil.isNotNull(lineManagementDOS)){
            ReportCreditLineManagementVO managementVO = lineManagementDOS.toVO();
            response.setData(managementVO);
        }

        return response;
    }

    public ApiResponse<ReportCreditLineManagementVO> queryForPage(ReportCreditLineManagementQueryREQ req) {
        ApiResponse<ReportCreditLineManagementVO> apiResponse = new ApiResponse<>();
        Map<String, Object> param = ObjConversionTool.convertMap(req);
        param.put("groupList", determineGroupQueryPermissions());
        ApiResponse<ReportCreditLineManagementDO> customPage = ApiUtil.getCustomPage(param, new SelectCallBack() {
            @Override
            public List<ReportCreditLineManagementDO> doInSelect(Map map) {
                return ReportCreditLineManagementDao.Instance.selectPage(param);

            }
        });
        List<ReportCreditLineManagementVO> managementVOS = customPage.getData().stream().map(ReportCreditLineManagementDO::toVO).collect(Collectors.toList());
        apiResponse.setData(managementVOS).setCount(customPage.getCount());
        return apiResponse;
    }

    /**
     * 查询账号的权限
     *
     * @return
     */
    public List<String> determineGroupQueryPermissions() {
        List<String> resList = new ArrayList<>();
        // 查询报告配置
        Map<String, Object> reportConfigParam = Maps.newHashMap();
        List<ReportConfigDO> configDOS = ReportConfigDao.Instance.selectPage(reportConfigParam);

        // 构建账户集合
        Set<String> zyAccountSet = configDOS.stream()
                .filter(x -> !CollUtil.contains(InvestableQuotaQueryService.ZY_NOT_IN_GROUP_NAMES, x.getGroupName()))
                .map(ReportConfigDO::getInsideSecAccName)
                .filter(StrUtil::isNotEmpty)
                .collect(Collectors.toSet());

        Set<String> marketMakingAccountSet = configDOS.stream()
                .filter(x -> InvestableQuotaQueryService.MARKETMAKING_GROUP_NAME.equals(x.getGroupName()))
                .map(ReportConfigDO::getInsideSecAccName)
                .filter(StrUtil::isNotEmpty)
                .collect(Collectors.toSet());
        // 获取用户权限账户
        String userName = UserSessionUtil.getUser().getUserName();
        List<String> userAccountList = accountTreeService.getAuthorityName(userName);
        // 判断权限
        if (zyAccountSet.stream().anyMatch(userAccountList::contains)) {
            resList.add(PTMS);
        }
        if (marketMakingAccountSet.stream().anyMatch(userAccountList::contains)) {
            resList.add(PMMS);
        }
        LogTool.info(BUSINESS_NAME + MAIN_COMPANY_LOG + "用户：{}，权限：{}", userName, resList);
        return resList;
    }

    @RedisLock(key = "calReportCreditLineManagement", expire = 10000)
    public void calCreditLine(String bizDate) {

        ManCompanyCreditBondReportREQ req = new ManCompanyCreditBondReportREQ();
        req.setPageNo(1);
        req.setPageSize(Integer.MAX_VALUE);
        ApiResponse<ManCompanyCreditBondReportVO> reportVOApiResponse = creditBondReportService.manCompanyQueryPage(req);
        List<ManCompanyCreditBondReportVO> dataList = reportVOApiResponse.getData();
        dataList = dataList.stream().filter(x -> ObjectUtil.isNotNull(x) && StrUtil.isNotEmpty(x.getCompany())).collect(Collectors.toList());
        if (CollUtil.isEmpty(dataList)) {
            LogTool.info(BUSINESS_NAME + MAIN_COMPANY_LOG + "查询信用债额度管理表为空，没有任何主体");
            return;
        }
        List<ReportCreditLineManagementDO> creditLineManagementDOList = new ArrayList<>();
        for (ManCompanyCreditBondReportVO creditBondReportVO : dataList) {
            if (StrUtil.isNotEmpty(creditBondReportVO.getPtQuota())
                    && BigDecimalUtil.compareRes(NumberUtil.toBigDecimal(creditBondReportVO.getPtQuota()), BigDecimal.ZERO) >= 0) {
                ReportCreditLineManagementDO managementDO = generateResDO(creditBondReportVO, PTMS, bizDate, creditBondReportVO.getPtQuota());
                creditLineManagementDOList.add(managementDO);
            }
            if (StrUtil.isNotEmpty(creditBondReportVO.getPmQuota())
                    && BigDecimalUtil.compareRes(NumberUtil.toBigDecimal(creditBondReportVO.getPmQuota()), BigDecimal.ZERO) >= 0) {
                ReportCreditLineManagementDO lineManagementDO = generateResDO(creditBondReportVO, PMMS, bizDate, creditBondReportVO.getPmQuota());
                creditLineManagementDOList.add(lineManagementDO);
            }
        }
        //按组别先划分
        Map<String, List<ReportCreditLineManagementDO>> lineManagementDataMap = creditLineManagementDOList.stream()
                .filter(ObjectUtil::isNotNull)
                .collect(Collectors.groupingBy(ReportCreditLineManagementDO::getBelongingGroup));

        //获取占用额度系数
        OccupancyQuotaVO quotaVO = new OccupancyQuotaVO();
        try {
            quotaVO = creditBondReportService.queryOccupancyQuota().getData().get(0);
        } catch (Exception e) {
            LogTool.error(BUSINESS_NAME + MAIN_COMPANY_LOG + "信用债额度获取异常{}", e);
        }
        Map<String, Object> param = Maps.newHashMap();
        param.put("del", 0);
        //查询个券额度表的数据
        param.clear();
        param.put("bizDate", bizDate);
        boolean dateCompareFlag = TimeTool.CURRENT.getCunrrentyyyyMMdd().compareTo(bizDate) < 0;
        if (dateCompareFlag) {
            Integer tnDay = ibTradeDateService.getTradeDayCountBetweenTwoDate(Integer.parseInt(TimeTool.CURRENT.getCunrrentyyyyMMdd()),
                    Integer.parseInt(bizDate)) - 1;
            LogTool.info(BUSINESS_NAME + MAIN_COMPANY_LOG + "传入日期：{}，Tn日值为：{}", bizDate, tnDay);
            DAssert.assertTrue(tnDay <= 7, PtmsErrCode.FICC_COMMON_ERROR.fillMsg("未来日期不可大于T+7"));
            param.put("tn", tnDay);
        }
        List<ReportCreditNewCouponsLineManagementBO> dataBOList;
        if (TimeTool.CURRENT.getCunrrentyyyyMMdd().compareTo(bizDate) <= 0) {
            param.put("bizDate", TimeTool.CURRENT.getCunrrentyyyyMMdd());
            dataBOList = ReportCreditNewCouponsLineManagementDao.Instance.selectPage(param);
        } else {
            dataBOList = ReportCreditNewCouponsLineManagementDao.Instance.selectHisPage(param);
        }
        if (dateCompareFlag) {
            //对于TN日期计算主体额度的时候做下处理，将值放到T0位置方便后续计算
            dataBOList.stream()
                    .filter(ObjectUtil::isNotNull)
                    .forEach(x -> {
                        x.setPositionAmountT0(x.getPositionAmountTN());
                        x.setAvailableAmountT0(x.getPositionAmountTN());
                        x.setOccupationQuotaT0(x.getOccupationQuotaTN());
                    });
        }
        //主体为空的个券数据不统计
        List<ReportCreditNewCouponsLineManagementVO> queryVOList = dataBOList.stream()
                .filter(x -> ObjectUtil.isNotNull(x) && StrUtil.isNotEmpty(x.getManCompany()))
                .map(ReportCreditNewCouponsLineManagementVO::toVO).collect(Collectors.toList());
        Map<String, List<ReportCreditNewCouponsLineManagementVO>> newCouponsLineManagementMap = queryVOList
                .stream()
                .filter(x -> ObjectUtil.isNotNull(x) && StrUtil.isNotEmpty(x.getGroupName()))
                .collect(Collectors.groupingBy(ReportCreditNewCouponsLineManagementVO::getGroupName));

        List<ReportCreditLineManagementDO> newCalMainCompanyLineAll = new ArrayList<>();
        for (String res : lineManagementDataMap.keySet()) {
            //组别下的所有数据 ,同一组别下的主体数据和个券数据
            List<ReportCreditNewCouponsLineManagementVO> resLists = newCouponsLineManagementMap.getOrDefault(res, new ArrayList<>());
            List<ReportCreditLineManagementDO> creditLineManagementDOS = lineManagementDataMap.get(res);
            newCalMainCompanyLineAll.addAll(newCalMainCompanyLineAll(resLists, creditLineManagementDOS, res, quotaVO));
        }
        if (CollUtil.isNotEmpty(newCalMainCompanyLineAll)) {
            //按业务日期删除数据
            ReportCreditLineManagementDao.Instance.deleteByBizDate(bizDate);
            ReportCreditLineManagementDao.Instance.insertBatchPool(newCalMainCompanyLineAll);
        }
    }

    /**
     * 这里的入口数据是组别维度的
     *
     * @param resLists
     * @param lineManagementDataList
     */
    private List<ReportCreditLineManagementDO> newCalMainCompanyLineAll(List<ReportCreditNewCouponsLineManagementVO> resLists, List<ReportCreditLineManagementDO> lineManagementDataList,
                                                                        String groupName, OccupancyQuotaVO quotaVO) {

        //按主体再划分
        Map<String, ReportCreditLineManagementDO> managementDOMap = lineManagementDataList.stream()
                .filter(x -> ObjectUtil.isNotNull(x) && StrUtil.isNotEmpty(x.getMainCompany()))
                .collect(Collectors.toMap(ReportCreditLineManagementDO::getMainCompany, x -> x));

        Map<String, List<ReportCreditNewCouponsLineManagementVO>> newCouponsLineManagementMap = resLists
                .stream()
                .filter(x -> ObjectUtil.isNotNull(x) && StrUtil.isNotEmpty(x.getManCompany()))
                .collect(Collectors.groupingBy(ReportCreditNewCouponsLineManagementVO::getManCompany));

        List<ReportCreditLineManagementDO> resLineManagementDOList = new ArrayList<>();


        managementDOMap.forEach((k, creditLineManagementDO) -> {
            if (ObjectUtil.isNull(creditLineManagementDO)) {
                return;
            }
            try {
                List<ReportCreditNewCouponsLineManagementVO> list = newCouponsLineManagementMap.getOrDefault(k, new ArrayList<>());
                LogTool.info("【信用债额度计算-主体逻辑计算】组别:{} 主体:{} 对应个券数据:{}", groupName, k, JSONUtil.toJsonStr(list));

                //限额
                BigDecimal quota = creditLineManagementDO.getQuota();

                //所有持仓面额
                BigDecimal totalPositionQuota = list.stream()
                        .filter(x -> ObjectUtil.isNotNull(x) && ObjectUtil.isNotNull(x.getPositionAmountT0()))
                        .map(ReportCreditNewCouponsLineManagementVO::getPositionAmountT0)
                        .map(BigDecimalUtil::tryParseDecimal)
                        .reduce(BigDecimal.ZERO, BigDecimalUtil::add);
                //同业存单持仓面额
                BigDecimal interbankDepositRemainingQuota = list.stream()
                        .filter(NCDS_FILTER)
                        .filter(x -> ObjectUtil.isNotNull(x) && ObjectUtil.isNotNull(x.getPositionAmountT0()))
                        .map(ReportCreditNewCouponsLineManagementVO::getPositionAmountT0)
                        .map(BigDecimalUtil::tryParseDecimal)
                        .reduce(BigDecimal.ZERO, BigDecimalUtil::add);
                //次级债、永续债、ABS持仓面额
                BigDecimal subordinatedAndABSRemainingQuota = list.stream()
                        .filter(ABS_OTHER_FILTER)
                        .filter(x -> ObjectUtil.isNotNull(x) && ObjectUtil.isNotNull(x.getPositionAmountT0()))
                        .map(ReportCreditNewCouponsLineManagementVO::getPositionAmountT0)
                        .map(BigDecimalUtil::tryParseDecimal)
                        .reduce(BigDecimal.ZERO, BigDecimalUtil::add);
                //普通债持仓面额
                BigDecimal generalRemainingQuota = BigDecimalUtil.subtract(totalPositionQuota, BigDecimalUtil.add(interbankDepositRemainingQuota, subordinatedAndABSRemainingQuota));

                creditLineManagementDO.setTotalPosition(totalPositionQuota);
                creditLineManagementDO.setInterbankDepositAllPosition(interbankDepositRemainingQuota);
                creditLineManagementDO.setSubordinatedAndABSAllPosition(subordinatedAndABSRemainingQuota);
                creditLineManagementDO.setGeneralAllPosition(generalRemainingQuota);

                //所有占用面额
                BigDecimal totalOccupationQuota = list.stream()
                        .filter(x -> ObjectUtil.isNotNull(x) && ObjectUtil.isNotNull(x.getOccupationQuotaT0()))
                        .map(ReportCreditNewCouponsLineManagementVO::getOccupationQuotaT0)
                        .map(BigDecimalUtil::tryParseDecimal)
                        .reduce(BigDecimal.ZERO, BigDecimalUtil::add);
                //设置占用额度
                creditLineManagementDO.setOccupancyQuota(totalOccupationQuota);
                //总剩余额度
                BigDecimal totalRemainingQuota = BigDecimalUtil.subtract(quota, totalOccupationQuota);
                //设置总剩余额度=限额-所有占用面额
                creditLineManagementDO.setTotalRemainingAmount(totalRemainingQuota);

                //同业存单剩余面额

                //剩余额度(万元)同业存单
                BigDecimal initialCreditLine = interbankDepositRemainingQuota(k, quotaVO.getInterbankDeposit(), totalRemainingQuota);
                //次级债、永续债、ABS剩余面额
                BigDecimal subordinatedAndABSRemainingQuota1 = subordinatedAndABSRemainingQuota(creditLineManagementDO.getInternalEvaluationLevel(), quotaVO.getSubordinatedAndABS(), totalRemainingQuota);
                //普通债剩余额度
                BigDecimal normalRemainingQuota = normalRemainingQuota(quotaVO.getGeneral(), totalRemainingQuota);
                creditLineManagementDO.setInterbankDepositRemainingAmount(initialCreditLine);
                creditLineManagementDO.setSubordinatedAndABSRemainingAmount(subordinatedAndABSRemainingQuota1);
                creditLineManagementDO.setGeneralRemainingAmount(normalRemainingQuota);
                resLineManagementDOList.add(creditLineManagementDO);
            } catch (Exception e) {
                LogTool.error(BUSINESS_NAME + MAIN_COMPANY_LOG + "信用债额度计算-主体逻辑计算异常{}", e);
            }
        });
        return resLineManagementDOList;
    }

    private ReportCreditLineManagementDO generateResDO(ManCompanyCreditBondReportVO creditBondReportVO, String groupName, String bizDate, String quota) {
        ReportCreditLineManagementDO creditLineManagementDO = new ReportCreditLineManagementDO();
        creditLineManagementDO.setEID(RandomUtil.randomUUID());
        creditLineManagementDO.setMainCompany(creditBondReportVO.getCompany());
        creditLineManagementDO.setInternalEvaluationLevel(creditBondReportVO.getRiskInternalEvaluation());
        creditLineManagementDO.setBelongingGroup(groupName);
        creditLineManagementDO.setBizDate(bizDate);
        creditLineManagementDO.setQuota(BigDecimalUtil.multiply(NumberUtil.toBigDecimal(quota), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum()));
        return creditLineManagementDO;
    }


    public ReportCreditLineManagementDO calMainCompanyLineAll(Map<String, List<PositionDataVO>> positionMap,
                                                              String mainCompany, BigDecimal bigDecimal,
                                                              int evaluationLevel, String bizDate, OccupancyQuotaVO quotaVO, BigDecimal currencyPair) {

        List<PositionDataVO> positionDataVOS = positionMap.get(mainCompany);
        BigDecimal ncdsOwnAmount = BigDecimal.ZERO;
        BigDecimal subordinatedAndABSOwnAmount = BigDecimal.ZERO;
        BigDecimal allOwnAmount = BigDecimal.ZERO;
        BigDecimal normalOwnAmount = BigDecimal.ZERO;

        if (CollUtil.isNotEmpty(positionDataVOS)) {
            List<String> strings = positionDataVOS.stream().map(PositionDataVO::getBondMarketCode).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
            LogTool.info(BUSINESS_NAME + "主体{}，下面涵盖的债券代码{}", mainCompany, strings);
            //1.先知道汇率是多少
            //2.知道每个持仓实体的币种，如果市场是其他并且币种是USD就将乘以汇率转换为人民币在添加
            //自营所有持仓(万元)-同业存单
            ncdsOwnAmount = BigDecimalUtil.divide(positionDataVOS.stream()
                    .filter(x -> ChoiceBondTypeEnum.NCDS.getName().equals(x.getRefBondType()))
                    .map(x -> processT7OwnAmount(x, currencyPair))
                    .reduce(BigDecimal.ZERO, BigDecimalUtil::add), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum(), 0);
            //自营所有持仓(万元)-次级债、永续债、ABS
            subordinatedAndABSOwnAmount = BigDecimalUtil.divide(positionDataVOS.stream()
                    .filter(x -> ChoiceBondTypeEnum.ABS.getName().equals(x.getRefBondType())
                            || BooleanEnum.TRUE.getName().equals(x.getPerpetualBond())
                            || BooleanEnum.TRUE.getName().equals(x.getStrIsmDebt()))
                    .map(x -> processT7OwnAmount(x, currencyPair))
                    .reduce(BigDecimal.ZERO, BigDecimalUtil::add), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum(), 0);
            //自营所有持仓(万元)-总面额
             allOwnAmount = BigDecimalUtil.divide(positionDataVOS.stream()
                     .filter(x -> !ChoiceBondTypeEnum.INRB.getName().equals(x.getRefBondType()))
                     .map(x -> processT7OwnAmount(x, currencyPair))
                    .reduce(BigDecimal.ZERO,BigDecimalUtil::add),NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum(),0);
            //自营所有持仓(万元)-普通债
             normalOwnAmount = BigDecimalUtil.subtract(allOwnAmount,BigDecimalUtil.add(subordinatedAndABSOwnAmount,ncdsOwnAmount));

        }
        //同业存单额度系数
        String interbankDeposit = quotaVO.getInterbankDeposit();
        //次级债、永续债、ABS额度系数
        String subordinatedAndABS = quotaVO.getSubordinatedAndABS();
        //普通债额度系数
        String general = quotaVO.getGeneral();
        //占用额度(万元)
        BigDecimal occupyQuota = BigDecimalUtil.add(calOccupancyQuota(normalOwnAmount, general),
                BigDecimalUtil.add(calOccupancyQuota(ncdsOwnAmount, interbankDeposit)
                        , calOccupancyQuota(subordinatedAndABSOwnAmount, subordinatedAndABS)), 0);
        BigDecimal mainCompanyQuota = ObjectUtil.isNull(bigDecimal) ? BigDecimal.ZERO : bigDecimal.setScale(2, RoundingMode.HALF_UP);
        //剩余额度(万元)总剩余额度 =  该主体[自营额度] - 占用额度
        BigDecimal totalRemainingQuota = BigDecimalUtil.subtract(BigDecimalUtil.multiply(mainCompanyQuota,NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum()),occupyQuota,0);
        //剩余额度(万元)同业存单
        BigDecimal interbankDepositRemainingQuota = interbankDepositRemainingQuota(mainCompany, interbankDeposit, totalRemainingQuota);
        //剩余额度(万元)次级债、永续债、ABS
        BigDecimal subordinatedAndABSRemainingQuota = subordinatedAndABSRemainingQuota(evaluationLevel, subordinatedAndABS, totalRemainingQuota);
        //剩余额度(万元)普通债
        BigDecimal normalRemainingQuota = normalRemainingQuota(general, totalRemainingQuota);

        return ReportCreditLineManagementDO.builder()
                .EID(RandomUtil.randomUUID())
                .bizDate(bizDate)
                .mainCompany(mainCompany)
                .internalEvaluationLevel(evaluationLevel)
                .interbankDepositAllPosition(ncdsOwnAmount)
                .subordinatedAndABSAllPosition(subordinatedAndABSOwnAmount)
                .generalAllPosition(normalOwnAmount)
                .totalPosition(allOwnAmount)
                .occupancyQuota(occupyQuota)
                .totalRemainingAmount(totalRemainingQuota)
                .interbankDepositRemainingAmount(interbankDepositRemainingQuota)
                .subordinatedAndABSRemainingAmount(subordinatedAndABSRemainingQuota)
                .generalRemainingAmount(normalRemainingQuota).build();
    }

    public String getT7OwnAmount(PositionDataVO positionDataVO) {
        if (ObjectUtil.isNull(positionDataVO) && ObjectUtil.isNull(positionDataVO.getOwnAmountTnVO())) {
            LogTool.error(BUSINESS_NAME + "getT7OwnAmount ownAmountTnVO is null,positionDataVO:{}", positionDataVO);
            return null;
        }
        return positionDataVO.getOwnAmountTnVO().getT7OwnAmount();
    }

    /**
     * 获取T+1日卖出可用来
     **/
    public String getT1AvailableAmount(PositionDataVO positionDataVO) {
        if (ObjectUtil.isNull(positionDataVO) || ObjectUtil.isNull(positionDataVO.getAvailableTnVO())) {
            LogTool.error(BUSINESS_NAME + "getT1AvailableAmount ownAmountTnVO is null,positionDataVO:{}", positionDataVO);
            return null;
        }
        return positionDataVO.getAvailableTnVO().getT1AvailableAmount();
    }



    private BigDecimal processT7OwnAmount(PositionDataVO x, BigDecimal currencyPair) {
        BigDecimal t7OwnMount = BigDecimalUtil.tryParseDecimal(getT7OwnAmount(x));

        if (OTHER.equals(x.getMarket()) && !CurrencyEnum.CNY.getFlag().equals(x.getCurrency())) {
            return BigDecimalUtil.multiply(t7OwnMount, currencyPair);
        } else {
            return t7OwnMount;
        }
    }

    /**
     * 剩余额度(万元)
     * 普通债
     * = 总剩余额度/普通债占用额度系数
     *
     * @param generalCoefficient  普通债占用额度系数
     * @param totalRemainingQuota 总剩余额度
     * @return BigDecimal
     */
    private BigDecimal normalRemainingQuota(String generalCoefficient, BigDecimal totalRemainingQuota) {
        BigDecimal coefficientDecimal = BigDecimalUtil.tryParseDecimal(generalCoefficient, BigDecimal.ZERO);
        if (BigDecimal.ZERO.compareTo(coefficientDecimal) == 0) {
            return BigDecimal.ZERO;
        }
        return BigDecimalUtil.divide(totalRemainingQuota, coefficientDecimal, 0);
    }


    /**
     * 剩余额度(万元)
     * 次级债、永续债、ABS
     *
     * 如果[内评等级] <=5，=总剩余额度/次级、永续、ABS占用额度系数
     * 否则=0
     * 前端显示保留整数
     *
     * @param internalEvaluationLevel 内评等级
     * @param isubordinatedAndABSCoefficient  次级、永续、ABS占用额度系数
     * @param totalRemainingQuota 总剩余额度
     * @return BigDecimal
     */
    private BigDecimal subordinatedAndABSRemainingQuota(int internalEvaluationLevel, String isubordinatedAndABSCoefficient,BigDecimal totalRemainingQuota){
        if(internalEvaluationLevel > 5){
            return BigDecimal.ZERO;
        }
        BigDecimal coefficientDecimal = BigDecimalUtil.tryParseDecimal(isubordinatedAndABSCoefficient,BigDecimal.ZERO);
        if(BigDecimal.ZERO.compareTo(coefficientDecimal) == 0){
            return BigDecimal.ZERO;
        }
        return BigDecimalUtil.divide(totalRemainingQuota,coefficientDecimal,0);
    }

    /**
     * 剩余额度(万元)
     * 同业存单
     *
     * 如果主体类型为银行（包含“银行”字符串），= 总剩余额度/同业存单占用额度系数；
     * 否则=0
     *
     * @param mainCompany 公司主体名称
     * @param interbankDepositCoefficient 同业存单额度系数
     * @param totalRemainingQuota 总剩余额度
     * @return BigDecimal
     */
    private BigDecimal interbankDepositRemainingQuota(String mainCompany, String interbankDepositCoefficient,BigDecimal totalRemainingQuota){
        if(!mainCompany.contains("银行")){
            return BigDecimal.ZERO;
        }
        BigDecimal coefficientDecimal = BigDecimalUtil.tryParseDecimal(interbankDepositCoefficient,BigDecimal.ZERO);
        if(BigDecimal.ZERO.compareTo(coefficientDecimal) == 0){
            return BigDecimal.ZERO;
        }
        return BigDecimalUtil.divide(totalRemainingQuota,coefficientDecimal,0);
    }

    private BigDecimal calOccupancyQuota(BigDecimal ownAmount,String coefficient){
        BigDecimal coefficientDecimal = BigDecimalUtil.tryParseDecimal(coefficient,BigDecimal.ZERO);
        if(BigDecimal.ZERO.compareTo(coefficientDecimal) == 0){
            LogTool.warn(BUSINESS_NAME+"占用额度系数非法或者零");
            return BigDecimal.ZERO;
        }
        return BigDecimalUtil.multiply(ownAmount, coefficientDecimal);

    }

    /**
     * 设置每条持仓的主体
     * 根据发行人担保人设置主体
     * 处理是否次级永续为空
     */
    public void handleChoiceParam(List<PositionDataVO> positionDataVOS,Map<String,Integer> leverMap){
        if(CollUtil.isEmpty(positionDataVOS)){
            return;
        }
        Map<String, BondBaseInfoRespDTO> baseInfoMap = getBondBaseInfoMap(positionDataVOS);
        for(PositionDataVO positionDataVO:positionDataVOS){
            //处理是否次级永续为空
            String marketCode = positionDataVO.getBondMarketCode();
            if (StrUtil.isEmpty(marketCode)) {
                continue;
            }
            //担保人
            String writer = positionDataVO.getUnderWriter();
            //发行人
            String instName = positionDataVO.getInstName();
            BondBaseInfoRespDTO baseInfoRespDTO = baseInfoMap.get(marketCode);
            if (StrUtil.isEmpty(writer) || StrUtil.isEmpty(instName)) {
                if (ObjectUtil.isNotNull(baseInfoRespDTO)) {
                    writer = StrUtil.isEmpty(writer) ? baseInfoRespDTO.getWtyCompname() : writer;
                    instName = StrUtil.isEmpty(instName) ? baseInfoRespDTO.getInstName() : instName;
                }
            }
            String ismDebt = positionDataVO.getStrIsmDebt();
            if (StrUtil.isEmpty(ismDebt)) {
                if (ObjectUtil.isNotNull(baseInfoRespDTO)) {
                    positionDataVO.setStrIsmDebt(baseInfoRespDTO.getStrIsMdebt());
                }
            }
            String perpetualBond = positionDataVO.getPerpetualBond();
            if (StrUtil.isEmpty(perpetualBond)) {
                if (ObjectUtil.isNotNull(baseInfoRespDTO)) {
                    positionDataVO.setPerpetualBond(baseInfoRespDTO.getPerpetualBond());
                }
            }

            if (StrUtil.isEmpty(writer) && StrUtil.isEmpty(instName)) {
                LogTool.info(BUSINESS_NAME + "{},担保人发行人均为空", marketCode);
                continue;
            }
            //如果发行人、担保人都在库，则统一占用担保人的额度；如果发行人在库且担保人不在库，则占用发行人的额度
            //10.10逻辑更改(1)
            // 对于非境外债，[所属主体]判断逻辑改为：
            //发行人不在库，则不计入任何额度
            //发行人的[风控内评]在1-6(闭区间)的，无论是否有担保方以及无论担保方的评级多少，都占用发行人的额度；
            //发行人的[风控内评]大于6的且担保人在库的，占用担保人的额度；
            //发行人的[风控内评]大于6的且担保人不在库的，占用发行人的额度；
            Integer writerLevel = leverMap.get(writer);
            Integer instNameLevel = leverMap.get(instName);
            if (ObjectUtil.isNull(instNameLevel)) {
                LogTool.info(BUSINESS_NAME + "{},发行人{},未在库", marketCode,instName);
                continue;
            }
            //发行人的[风控内评]在1-6(闭区间)的，无论是否有担保方以及无论担保方的评级多少，都占用发行人的额度；
            if (instNameLevel <= 6) {
                positionDataVO.setManCompany(instName);
            } else {
                if (StrUtil.isNotEmpty(writer) && ObjectUtil.isNotNull(writerLevel)) {
                    //发行人的[风控内评]大于6的且担保人在库的，占用担保人的额度；
                    positionDataVO.setManCompany(writer);
                } else {
                    //发行人的[风控内评]大于6的且担保人不在库的，占用发行人的额度；
                    positionDataVO.setManCompany(instName);
                }
            }
        }

        //查中资美债币种
        Map<String, String> bondCurrency = getOffshoreBondCurrency(positionDataVOS);
        //设置他们对应的币种
        positionDataVOS
                .stream()
                .filter(ObjectUtil::isNotNull)
                .filter(positionDataVO -> CharSequenceUtil.isNotEmpty(positionDataVO.getMarket())
                        && CharSequenceUtil.equals(positionDataVO.getMarket(), OTHER)
                        && CharSequenceUtil.isNotEmpty(positionDataVO.getBondMarketCode()))
                .forEach(x->{
                    String bondMarketCode = x.getBondMarketCode();
                    String currency = bondCurrency.get(bondMarketCode);
                    if (StrUtil.isNotEmpty(currency)) {
                        x.setCurrency(currency);
                    }
                });

    }

    private Map<String,BondBaseInfoRespDTO> getBondBaseInfoMap(List<PositionDataVO> positionDataVOS){
        Map<String, List<PositionDataVO>> paramMap = positionDataVOS.stream()
                .filter(x->StrUtil.isNotEmpty(x.getMarket()))
                .filter(x -> StrUtil.isEmpty(x.getUnderWriter())
                || StrUtil.isEmpty(x.getInstName())
                || StrUtil.isEmpty(x.getStrIsmDebt())
                || StrUtil.isEmpty(x.getPerpetualBond()))
                .collect(Collectors.groupingBy(PositionDataVO::getMarket));
        Map<String, BondBaseInfoRespDTO> resMap = new HashMap<>();
        for (Map.Entry<String, List<PositionDataVO>> entry : paramMap.entrySet()) {
            String key = entry.getKey();
            List<PositionDataVO> value = entry.getValue();
            HtMarketEnum byName = HtMarketEnum.getEnumByName(key);
            if(ObjectUtil.isNull(byName)){
                continue;
            }
            List<String> bondCodeList = value.stream()
                    .map(PositionDataVO::getBondCode)
                    .filter(StrUtil::isNotEmpty)
                    .distinct()
                    .collect(Collectors.toList());
            LogTool.info(BUSINESS_NAME+"市场{}，债券代码{},担保人,发行人，是否次级，是否永续缺失，补偿查询",key,bondCodeList);
            try {
                // 批量获取债券基本信息map
                Map<String, BondBaseInfoRespDTO> bondBaseInfoRespDTOMap = bondChoiceService
                        .queryBondBaseInfoFromChoice(bondCodeList, byName)
                        .stream()
                        .filter(x -> StrUtil.isNotEmpty(x.getSecuCode()))
                        .collect(Collectors.toMap(BondBaseInfoRespDTO::getSecuCode, Function.identity(), (oldKey, newKey) -> newKey));
                if (CollUtil.isNotEmpty(bondBaseInfoRespDTOMap)) {
                    resMap.putAll(bondBaseInfoRespDTOMap);
                }
            } catch (Exception e) {
                LogTool.error(BUSINESS_NAME + "获取choice信息失败,{}", e);
            }

        }
        return resMap;
    }

    private int compareInteger(Integer a, Integer b) {
        if (ObjectUtil.isNull(a)) {
            return 1;
        }
        if (ObjectUtil.isNull(b)) {
            return -1;
        }
        return a.compareTo(b);
    }

    /**
     * 结合系统维护持仓和choice，返回境外债币种况
     */
    public Map<String, String> getOffshoreBondCurrency(List<PositionDataVO> positionDataVOList) {
        Map<String, String> currencyMap = new HashMap<>();
        //获取中资美债及他对应的币种
        List<OffshoreBondPositionDO> offshoreBondPositionDOS = OffshoreBondPositionDao.Instance.selectBondCodeCurrency();
        currencyMap = offshoreBondPositionDOS
                .stream()
                .filter(x -> ObjectUtil.isNotNull(x)
                        && StrUtil.isNotEmpty(x.getBondMarketCode())
                        && StrUtil.isNotEmpty(x.getCurrency()))
                .collect(Collectors.toMap(OffshoreBondPositionDO::getBondMarketCode,
                        x -> {
                            CurrencyEnum currencyEnum = CurrencyEnum.getByCode(x.getCurrency());
                            if (ObjectUtil.isNull(currencyEnum)) {
                                return CurrencyEnum.USD.getFlag();
                            }
                            return currencyEnum.getFlag();
                        }, (oldVal, newVal) -> newVal));


        List<String> maintainOffshoreBondList = offshoreBondPositionDOS
                .stream()
                .filter(x -> ObjectUtil.isNotNull(x) && StrUtil.isNotEmpty(x.getBondMarketCode()))
                .map(OffshoreBondPositionDO::getBondMarketCode)
                .collect(Collectors.toList());


        List<String> systemOffshoreBondList = positionDataVOList.stream()
                .filter(x -> ObjectUtil.isNotNull(x)
                        && ReportCreditLineManagementService.OTHER.equals(x.getMarket())
                        && CharSequenceUtil.isNotEmpty(x.getBondMarketCode()))
                .map(PositionDataVO::getBondMarketCode)
                .collect(Collectors.toList());

        maintainOffshoreBondList.addAll(systemOffshoreBondList);

        List<String> queryList = maintainOffshoreBondList.stream().distinct().collect(Collectors.toList());


        List<BondCDBInfoRespDTO> bondCDBInfoRespDTOS = bondChoiceService.queryChoiceCDBInfo(queryList);
        currencyMap.putAll(bondCDBInfoRespDTOS.stream()
                .filter(x -> ObjectUtil.isNotNull(x)
                        && CharSequenceUtil.isNotEmpty(x.getSecurityCode())
                        && CharSequenceUtil.isNotEmpty(x.getCurrency()))
                .collect(Collectors.toMap(BondCDBInfoRespDTO::getSecurityCode, BondCDBInfoRespDTO::getCurrency, (oldVal, newVal) -> oldVal)));
        return currencyMap;
    }


    public ApiResponse<String> queryForCompany(ReportCreditLineManagementMainCompanyQueryREQ req) {
        return req.selectPage(() ->
                ReportCreditLineManagementDao.Instance.queryMainCompany(req.getBizDate(), req.getSearchKey())
        );
    }

    public void export(ReportCreditLineManagementQueryREQ req, HttpServletResponse response) {
        setRequestParams(req);
        ApiResponse<ReportCreditLineManagementVO> pageRes = queryForPage(req);
        List<ReportCreditLineManagementVO> managementVOApiResponseData = pageRes.getData();
        ApiResponse<ReportCreditLineManagementVO> totalRes = queryForTotal(req);
        List<ReportCreditLineManagementVO> totalData = totalRes.getData();
        ReportCreditLineManagementVO managementVO = CollUtil.get(totalData, 0);
        managementVO.setMainCompany("汇总");
        CollUtil.addAll(managementVOApiResponseData,managementVO);
        List<ReportCreditLineManagementExcelVO> data = BeanUtil.copyToList(managementVOApiResponseData, ReportCreditLineManagementExcelVO.class);
        if(CollUtil.isEmpty(data)){
            return;
        }
        data.get(data.size() - 1).setInternalEvaluationLevel("");
        String fileName = "信用债额度管理";
        try {
            FileUtil.responseHeader(response, fileName);
            ExcelWriter writer = EasyExcel.write(response.getOutputStream(), ReportCreditLineManagementExcelVO.class).build();
            WriteSheet sheet = EasyExcel.writerSheet(0, req.getBizDate()).build();
            writer.write(data, sheet);
            writer.finish();
        } catch (Exception e) {
            LogTool.error(BUSINESS_NAME+"导出失败,{}",e);
        }
    }


    private void setRequestParams(ReportCreditLineManagementQueryREQ req) {
        req.setPageNo(1);
        req.setPageSize(Integer.MAX_VALUE);
        if (CharSequenceUtil.isBlank(req.getBizDate())) {
            String tradeDate = com.eastmoney.digi.common.util.TimeTool.CURRENT.getCunrrentyyyyMMdd();
            req.setBizDate(tradeDate);
        }
    }

    public ApiResponse<ArrangeBondDetailVO> queryPositionDetailList(PositionDataQueryREQ req) {
        ApiResponse<ArrangeBondDetailVO> response = new ApiResponse<>();
        response.setOk();
        Integer queryDay = req.getBizDate();
        Map<String, Object> params = ObjConversionTool.convertMap(req);
        LogTool.info(BUSINESS_NAME + "信用债个券明细查询参数,{}", req);
        params.put("queryStartDate", queryDay.toString());
        Integer tnDay = 0;
        if (ObjectUtil.isNotNull(req.getTn())) {
            tnDay = req.getTn();
        }
        String nextTradeDate = queryDay.toString();
        if (tnDay > 0) {
            nextTradeDate = ibTradeDateService.getDateAddTradeDay(queryDay, tnDay);
        }
        params.put("queryEndDate", nextTradeDate);
        List<ArrangeBondDetailBO> arrangeBondDetailBOList = TradeChangeDetailDao.Instance.queryPositionDetailBOList(params);
        List<ArrangeBondDetailVO> bondDetailVOList = getArrangeBondDetailVOList(arrangeBondDetailBOList, req.getMarket());
        try {
            if (tnDay > 0) {
                List<ArrangeBondDetailVO> expirationDetails = positionDataService.getExpirationDetails(req, tnDay);
                bondDetailVOList.addAll(expirationDetails);
            }
        } catch (Exception e) {
            LogTool.error(BUSINESS_NAME + "查询失败到期明细失败,{}", e);
        }
        return response.setData(bondDetailVOList);
    }
    public List<ArrangeBondDetailVO> getArrangeBondDetailVOList(List<ArrangeBondDetailBO> arrangeBondDetailBOList,String market){
        if(CollUtil.isEmpty(arrangeBondDetailBOList)){
            return new ArrayList<>();
        }
        String code  = arrangeBondDetailBOList.get(0).getBondCode();
        HtMarketEnum enumByName = HtMarketEnum.getEnum(market);
        //剔除买断式回购和债券借贷(标的券)的变动明细）

        List<BondBaseInfoRespDTO> respDTOS = enumByName != null ?
                bondChoiceService.queryBondBaseInfoFromChoice(Collections.singletonList(code),enumByName) : null;
        return arrangeBondDetailBOList.stream().
                map(x -> new ArrangeBondDetailVO(x, CollUtil.isEmpty(respDTOS) ? null : respDTOS.get(0).getBondPeriod()))
                .filter(item -> TRADE_STATUS_LIST.contains(item.getTradingStatus()))
                .filter(item -> {
                    boolean outRight = OUT_RIGHT_TRADE_DIRECTION.contains(item.getTradingDirection());
                    boolean bdLend = LEND_TRADE_DIRECTION.contains(item.getTradingDirection()) && !ZY_DIRECTION.contains(item.getChangeDirection());
                    return !outRight && !bdLend;
                })
                .collect(Collectors.toList());
    }

    /**
     * 组装系统内持仓和导入的境外持仓
     * 过滤系统内已有的持仓，按债券代码维度
     * 原因：债券在系统持仓内有了，他下面所有的内证账号的持仓都会存在，不需要文件里导入的了
     * <p>
     * 10.10修改逻辑，境外债持仓表里有维护系统里的数据了，所以不需要过滤了，将系统内查出来的持仓过滤掉境外债，
     *
     * @param param
     */
    public List<PositionDataVO> assemblePosition(Map<String, Object> param) {
        List<PositionDataVO> resList = new ArrayList<>();
        //查询导入境外持仓，并转会为系统持仓
        List<OffshoreBondPositionDO> bondPositionDOS = OffshoreBondPositionDao.Instance.selectRes(param);
        List<PositionDataVO> importedPositionDataList = bondPositionDOS
                .stream()
                .filter(ObjectUtil::isNotNull)
                .map(this::convertToPositionData)
                .collect(Collectors.toList());

        //查询系统债券持仓,排除掉中资美债，因为已经通过刷新刷到境外债持仓维护页面了
        List<PositionDataVO> systemPositionDataVOList = PositionDataDao.Instance.queryHisOwnAmountPositionData(param);
        List<PositionDataVO> filterSystemPositionList = systemPositionDataVOList
                .stream()
                .filter(x -> ObjectUtil.isNotNull(x) && !OTHER.equals(x.getMarket()))
                .collect(Collectors.toList());
        resList.addAll(importedPositionDataList);
        resList.addAll(filterSystemPositionList);
        return resList;
    }

    public PositionDataVO convertToPositionData(OffshoreBondPositionDO offshoreBondPositionDO) {
        PositionDataVO positionDataVO = new PositionDataVO();
        OwnAmountTnVO ownAmountTnVO = new OwnAmountTnVO();
        positionDataVO.setBondCode(offshoreBondPositionDO.getBondMarketCode());
        positionDataVO.setBondMarketCode(offshoreBondPositionDO.getBondMarketCode());
        positionDataVO.setBondName(offshoreBondPositionDO.getBondName());
        positionDataVO.setInsideSecAccName(offshoreBondPositionDO.getInsideSecAccName());
        String positionAmount =  Optional.ofNullable(offshoreBondPositionDO.getPositionAmount())
                .map(x -> BigDecimalUtil.multiply(x, NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum(), 0).stripTrailingZeros().toPlainString()).orElse(null);

        ownAmountTnVO.setT7OwnAmount(Optional.ofNullable(offshoreBondPositionDO.getPositionAmount())
                .map(x -> BigDecimalUtil.multiply(x,NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum(),0).stripTrailingZeros().toPlainString()).orElse(null));
        positionDataVO.setOwnAmountTnVO(ownAmountTnVO);
        //设置境外债持仓的当日可用量和T+1日可用量
        AvailableTnVO availableTnVO = new AvailableTnVO();
        availableTnVO.setT1AvailableAmount(positionAmount);
        positionDataVO.setAvailableTnVO(availableTnVO);
        positionDataVO.setAvailableAmount(positionAmount);

        positionDataVO.setManCompany(offshoreBondPositionDO.getMainCompany());
        //个券明细表必须要插入证券账号代码
        positionDataVO.setInsideSecAccNo(offshoreBondPositionDO.getInsideSecAccName());
        positionDataVO.setMarket(OTHER);
        //设置债券类型
        OffshorePositionBondTypeEnum.setValue(positionDataVO, offshoreBondPositionDO.getBondType());
        positionDataVO.setCtz(offshoreBondPositionDO.getIsItACityInvestment()==1);
        positionDataVO.setCurrency(offshoreBondPositionDO.getCurrency());
        positionDataVO.setArea(offshoreBondPositionDO.getRegion());
        positionDataVO.setRegion(offshoreBondPositionDO.getRegion());
        return positionDataVO;
    }


}
