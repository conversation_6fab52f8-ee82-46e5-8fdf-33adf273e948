package com.eastmoney.ptms.ficc.mapper;

import com.eastmoney.ptms.dao.core.BasePtmsMapper;
import com.eastmoney.ptms.data.ficc.DO.ConvertibleBondPositionDetailDO;
import com.eastmoney.ptms.data.ficc.VO.ConvertibleBondVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName ConvertibleBondPositionOverviewMapper
 * @Description
 * @date 2024/02/08
 */
public interface ConvertibleBondPositionDetailMapper extends BasePtmsMapper<ConvertibleBondPositionDetailDO, String> {

    List<ConvertibleBondPositionDetailDO> queryHisRecords(@Param("bizDate") Integer bizDate, @Param("bondMarketCode") String bondMarketCode, @Param("fundId") Integer fundId);

    Integer mergeList(List<ConvertibleBondPositionDetailDO> list);

    List<ConvertibleBondVO> queryBond(@Param("bizDate") Integer bizDate, @Param("searchKey") String searchKey);
}
