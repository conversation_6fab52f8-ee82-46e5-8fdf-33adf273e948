package com.eastmoney.ptms.data.ficc.VO;

import cn.hutool.core.lang.Opt;
import com.eastmoney.ptms.data.ficc.DO.ConvertibleBondPositionDetailDO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;

/**
 * 可转债损益报表-持仓明细
 *
 * <AUTHOR>
 * @Date 2024/02/07 10:11
 */
@Getter
@Setter
@ToString
public class ConvertibleBondPositionDetailVO {

    /**
     * 债券代码带后缀
     */
    private String bondMarketCode;

    /**
     * 债券名称
     */
    private String bondName;

    /**
     * 基金编号
     */
    private Integer fundId;

    /**
     * 证券类型，5-可转债 ，F-开放式基金
     */
    private String stockType;

    /**
     * 最新持仓量
     */
    private BigInteger currentAmount;


    /**
     * 昨日持仓量
     */
    private BigInteger yesterdayAmount;

    /**
     * 当前市值
     */
    private BigInteger currentMarketValue;


    /**
     * 当日买量
     */
    private BigInteger buyAmount;

    /**
     * 当日卖量
     */
    private BigInteger saleAmount;


    /**
     * 昨收盘，4位小数
     */
    private BigDecimal preClose;

    /**
     * 最新价，4位小数
     */
    private BigDecimal close;


    /**
     * 实时涨跌，显示百分数，百分号后2位小数
     */
    private BigDecimal ratio;

    /**
     * 实时盈亏
     */
    private BigInteger currentProfitAndLoss;

    /**
     * 债券评级
     */
    private String  BDRating;

    /**
     * 成本价（净价）
     */
    private BigDecimal costPrice;

    /**
     * 盈亏率（%）
     */
    private BigDecimal  profitAndLossRatio;

    public static ConvertibleBondPositionDetailVO fromDetailDO(ConvertibleBondPositionDetailDO detailDO) {
        ConvertibleBondPositionDetailVO convertibleBondPositionDetailVO = new ConvertibleBondPositionDetailVO();
        convertibleBondPositionDetailVO.setBondMarketCode(detailDO.getBondMarketCode());
        convertibleBondPositionDetailVO.setBondName(detailDO.getBondName());
        convertibleBondPositionDetailVO.setFundId(detailDO.getFundId());
        convertibleBondPositionDetailVO.setStockType(detailDO.getStockType());
        convertibleBondPositionDetailVO.setCurrentMarketValue(Opt.ofNullable(detailDO.getCurrentMarketValue()).map(v -> v.setScale(0, RoundingMode.HALF_UP)).map(BigDecimal::toBigInteger).orElse(null));
        convertibleBondPositionDetailVO.setCurrentAmount(Opt.ofNullable(detailDO.getCurrentAmount()).map(v -> v.setScale(0, RoundingMode.HALF_UP)).map(BigDecimal::toBigInteger).orElse(null));
        convertibleBondPositionDetailVO.setBuyAmount(Opt.ofNullable(detailDO.getBuyAmount()).map(v -> v.setScale(0, RoundingMode.HALF_UP)).map(BigDecimal::toBigInteger).orElse(null));
        convertibleBondPositionDetailVO.setSaleAmount(Opt.ofNullable(detailDO.getSaleAmount()).map(v -> v.setScale(0, RoundingMode.HALF_UP)).map(BigDecimal::toBigInteger).orElse(null));
        convertibleBondPositionDetailVO.setYesterdayAmount(Opt.ofNullable(detailDO.getYesterdayAmount()).map(v -> v.setScale(0, RoundingMode.HALF_UP)).map(BigDecimal::toBigInteger).orElse(null));
        convertibleBondPositionDetailVO.setPreClose(Opt.ofNullable(detailDO.getPreClose()).map(v -> v.setScale(4, RoundingMode.HALF_UP)).orElse(null));
        convertibleBondPositionDetailVO.setClose(Opt.ofNullable(detailDO.getClose()).map(v -> v.setScale(4, RoundingMode.HALF_UP)).orElse(null));
        convertibleBondPositionDetailVO.setRatio(Opt.ofNullable(detailDO.getRatio()).map(v -> v.setScale(2, RoundingMode.HALF_UP)).orElse(null));
        convertibleBondPositionDetailVO.setCurrentProfitAndLoss(Opt.ofNullable(detailDO.getCurrentProfitAndLoss()).map(v -> v.setScale(0, RoundingMode.HALF_UP)).map(BigDecimal::toBigInteger).orElse(null));
        convertibleBondPositionDetailVO.setBDRating(detailDO.getBDRating());
        convertibleBondPositionDetailVO.setCostPrice(Opt.ofNullable(detailDO.getCostPrice()).map(v -> v.setScale(4, RoundingMode.HALF_UP)).orElse(null));
        convertibleBondPositionDetailVO.setProfitAndLossRatio(Opt.ofNullable(detailDO.getProfitAndLossRatio()).map(v -> v.setScale(2, RoundingMode.HALF_UP)).orElse(null));
        return convertibleBondPositionDetailVO;
    }
}
