package com.eastmoney.ptms.data.ficc.BO;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 债券资格检查信息封装类
 * 用于封装债券相关的所有检查参数，简化方法调用
 *
 * <AUTHOR>
 * @date 2025-01-23
 */
@Data
public class BondEligibilityInfo {
    /** 是否境外债 */
    private final boolean offshore;
    /** Choice东财债券一级分类 */
    private final String strDczcYjfl2021;
    /** Choice东财债券二级分类 */
    private final String strDczcEjfl2021;
    /** 发行人名称 */
    private final String instName;
    /** 担保人名称 */
    private final String wtyCompname;
    /** 是否永续债 */
    private final boolean isPerpetual;
    /** 是否次级债 */
    private final boolean isMdebt;
    /** 剩余期限 */
    private final BigDecimal remainingTermYear;
    /** 是否含利率跳升机制 */
    private final boolean interestRateJump;
    /** 是否含回售条款 */
    private final boolean hasPutOption;
    /** 是否城投 */
    private final boolean ctz;
    /** 发行人内评等级 */
    private final Integer instNameEvaluation;
    /** 担保人内评等级 */
    private final Integer wtyCompnameEvaluation;

    public BondEligibilityInfo(boolean offshore, String strDczcYjfl2021, String strDczcEjfl2021,
                               String instName, String wtyCompname, boolean isPerpetual, boolean isMdebt,
                               BigDecimal remainingTermYear, boolean interestRateJump, boolean hasPutOption,
                               boolean ctz, Integer instNameEvaluation, Integer wtyCompnameEvaluation) {
        this.offshore = offshore;
        this.strDczcYjfl2021 = strDczcYjfl2021;
        this.strDczcEjfl2021 = strDczcEjfl2021;
        this.instName = instName;
        this.wtyCompname = wtyCompname;
        this.isPerpetual = isPerpetual;
        this.isMdebt = isMdebt;
        this.remainingTermYear = remainingTermYear;
        this.interestRateJump = interestRateJump;
        this.hasPutOption = hasPutOption;
        this.ctz = ctz;
        this.instNameEvaluation = instNameEvaluation;
        this.wtyCompnameEvaluation = wtyCompnameEvaluation;
    }


}
