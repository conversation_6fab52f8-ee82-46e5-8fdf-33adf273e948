package com.eastmoney.ptms.ficc.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.eastmoney.digi.common.util.BigDecimalUtil;
import com.eastmoney.digi.common.util.CollectUtil;
import com.eastmoney.digi.common.util.FileUtil;
import com.eastmoney.digi.common.util.RandomUtil;
import com.eastmoney.digi.core.api.ApiResponse;
import com.eastmoney.digi.core.api.ApiUtil;
import com.eastmoney.digi.core.api.EmSort;
import com.eastmoney.digi.core.api.SelectCallBack;
import com.eastmoney.digi.core.redis.lock.RedisLock;
import com.eastmoney.ptms.dao.core.UserSessionUtil;
import com.eastmoney.ptms.data.enums.BooleanEnum;
import com.eastmoney.ptms.data.enums.NumberUnitEnum;
import com.eastmoney.ptms.data.ficc.BO.BondInfoDataBO;
import com.eastmoney.ptms.data.ficc.BO.InvestableResultBO;
import com.eastmoney.ptms.data.ficc.BO.ReportCreditNewCouponsLineManagementBO;
import com.eastmoney.ptms.data.ficc.BO.ReportCreditNewRegionLimitManagementBO;
import com.eastmoney.ptms.data.ficc.DO.OffshoreBondPositionDO;
import com.eastmoney.ptms.data.ficc.DO.ReportConfigDO;
import com.eastmoney.ptms.data.ficc.DO.ReportCreditNewCouponsLineManagementDO;
import com.eastmoney.ptms.data.ficc.DO.ReportCreditNewCouponsLineManagementTnDO;
import com.eastmoney.ptms.data.ficc.DO.ReportCreditNewRegionLimitManagementDO;
import com.eastmoney.ptms.data.ficc.DO.ReportCreditNewRegionManagementDO;
import com.eastmoney.ptms.data.ficc.DO.ReportParamConfigDO;
import com.eastmoney.ptms.data.ficc.DTO.choice.BondFxIndicatoraRespDTO;
import com.eastmoney.ptms.data.ficc.REQ.CommonBondSearchKeyREQ;
import com.eastmoney.ptms.data.ficc.REQ.CommonSearchREQ;
import com.eastmoney.ptms.data.ficc.REQ.ReportCreditNewCouponsLineManagementQueryREQ;
import com.eastmoney.ptms.data.ficc.VO.CommonBondSearchKeyVO;
import com.eastmoney.ptms.data.ficc.VO.ManCompanyCreditBondReportVO;
import com.eastmoney.ptms.data.ficc.VO.ManCompanySearchVO;
import com.eastmoney.ptms.data.ficc.VO.PositionDataVO;
import com.eastmoney.ptms.data.ficc.VO.ReportCreditNewCouponsLineManagementExcelVO;
import com.eastmoney.ptms.data.ficc.VO.ReportCreditNewCouponsLineManagementVO;
import com.eastmoney.ptms.ficc.choice.BondChoiceService;
import com.eastmoney.ptms.ficc.common.CommonConstants;
import com.eastmoney.ptms.ficc.dao.ManCompanyCreditBondReportBaseInfoDao;
import com.eastmoney.ptms.ficc.dao.OffshoreBondPositionDao;
import com.eastmoney.ptms.ficc.dao.PositionDataDao;
import com.eastmoney.ptms.ficc.dao.ReportConfigDao;
import com.eastmoney.ptms.ficc.dao.ReportCreditNewCouponsLineManagementDao;
import com.eastmoney.ptms.ficc.dao.ReportCreditNewCouponsLineManagementTnDao;
import com.eastmoney.ptms.ficc.dao.ReportCreditNewRegionLimitManagementDao;
import com.eastmoney.ptms.ficc.dao.ReportCreditNewRegionManagementDao;
import com.eastmoney.ptms.ficc.dao.ReportParamConfigDao;
import com.eastmoney.ptms.ficc.enums.ChoiceBondTypeEnum;
import com.eastmoney.ptms.ficc.enums.HtOrderStatusEnum;
import com.eastmoney.ptms.ficc.enums.ManualBondGroupTypeEnum;
import com.eastmoney.ptms.ficc.tool.TimeTool;
import com.eastmoney.ptms.ficc.tool.conversion.ObjConversionTool;
import com.eastmoney.ptms.ficc.tool.log.LogTool;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description:信用债个券额度管理新 Service
 * @author: caoyang
 * @since: 2025-08-01 14:59
 * @version: 1.0
 */
@Service
@RequiredArgsConstructor
public class ReportCrediNewCouponsLineManagementService {


    private final AccountTreeService accountTreeService;
    private final BondChoiceService bondChoiceService;
    private final ReportCreditLineManagementService reportCreditLineManagementService;
    private final InvestableQuotaQueryService investableQuotaQueryService;
    public static final List<String> DAY_END_STATUS_CODE = CollectUtil.asList(HtOrderStatusEnum.CONFIRM.getCode(), HtOrderStatusEnum.SETTLE_SUCCESS.getCode(),
            HtOrderStatusEnum.END_UNSETTLED.getCode(), HtOrderStatusEnum.END_SETTLED.getCode());


    public static final List<String> TRADE_STATUS_LIST = ImmutableList.of(HtOrderStatusEnum.CREATE.getCode(),
            HtOrderStatusEnum.PREAUDIT.getCode(), HtOrderStatusEnum.AUDIT.getCode(), HtOrderStatusEnum.PASS.getCode(),
            HtOrderStatusEnum.EXE.getCode(), HtOrderStatusEnum.CONFIRM.getCode(), HtOrderStatusEnum.SETTLE_SUCCESS.getCode(),
            HtOrderStatusEnum.END_UNSETTLED.getCode(), HtOrderStatusEnum.END_SETTLED.getCode());

    private static final String BUSINESS_NAME = "【信用债个券额度管理】-";

    public static final String OTHER = "OTHER";

    public static final String BIZ_TYPE = "报表管理-占用额度系数";
    public static final String INTERBANK_DEPOSIT_PARAM_NAME = "同业存单";
    public static final String SUBORDINATED_ANDABS_PARAM_NAME = "次级债&永续债&ABS";
    public static final String GENERAL_PARAM_NAME = "普通债";

    private static final List<String> tnPropertyNameList = Arrays.asList("positionAmountTN", "occupationQuotaTN", "availableAmountTN");

    public ApiResponse<CommonBondSearchKeyVO> queryBond(CommonBondSearchKeyREQ req) {
        return req.selectPage(() -> ReportCreditNewCouponsLineManagementDao.Instance.queryBond(req.getBondSearchKey()));
    }

    public ApiResponse<ManCompanySearchVO> queryManCompany(CommonSearchREQ req) {
        return req.selectPage(() -> ReportCreditNewCouponsLineManagementDao.Instance.queryManCompany(req.getSearchKey()));
    }

    /**
     * @param type 1:查询当日数据 2:查询昨日数据
     * @return void
     * @Date 2025-07-17
     * @throw
     **/
    @RedisLock(key = "calCreditNewCouponsLineManagement", expire = 10000)
    public void calCreditLine(Integer type) {
        Map<String, Object> param = Maps.newHashMap();
        Map<String, Object> reportConfigParam = Maps.newHashMap();
        List<String> resList = ImmutableList.of("可转债交易", "投资顾问");
        reportConfigParam.put("groupNameNotEqualList", resList);
        List<ReportConfigDO> configDOS = ReportConfigDao.Instance.selectPage(reportConfigParam);
        //所属组别为做市交易时，属于”做市“；其余属于”自营“
        Map<String, Set<String>> groupNameMap = configDOS
                .stream()
                .filter(v -> StrUtil.isNotEmpty(v.getInsideSecAccName()))
                .collect(Collectors.groupingBy(v -> "做市交易".equals(v.getGroupName()) ? ManualBondGroupTypeEnum.MARKET_MAKING.getName() : ManualBondGroupTypeEnum.PROPRIETARY.getName(),
                        Collectors.mapping(ReportConfigDO::getInsideSecAccName, Collectors.toSet())));
        List<String> nameList = configDOS.stream().map(ReportConfigDO::getInsideSecAccName)
                .filter(StrUtil::isNotEmpty).distinct().collect(Collectors.toList());
        LogTool.info(BUSINESS_NAME + "查询报表配置表内证券账号：{}", nameList);
        //判断查询日期是否是当日，以区分查询的交易状态
        if (CommonConstants.CAL_CREDIT_LINE_REALTIME.equals(type)) {
            param.put("tradingStatusList", TRADE_STATUS_LIST);
        } else {
            param.put("tradingStatusList", DAY_END_STATUS_CODE);
        }
        param.put("bizDate", TimeTool.CURRENT.getCunrrentyyyyMMdd());

        StopWatch watch = new StopWatch();
        watch.start();

        List<PositionDataVO> positionDataByInsideSecList = new ArrayList<>();
        if (CollUtil.isNotEmpty(nameList)) {
            //查询业务日期持仓信息 含内部证券账号信息
            param.put("insideSecAccNames", nameList);
            param.put("needInsideSec", "true");
            positionDataByInsideSecList = assemblePosition(param);
        } else {
            LogTool.info(BUSINESS_NAME + "内部证券账号设置为空");
        }
        watch.stop();
        LogTool.info(BUSINESS_NAME + "查询持仓信息耗时{}", watch.getTime());

        saveCouponsLineManagement(positionDataByInsideSecList, groupNameMap, TimeTool.CURRENT.getCunrrentyyyyMMdd());
    }

    public ApiResponse<ReportCreditNewCouponsLineManagementVO> queryForPage(ReportCreditNewCouponsLineManagementQueryREQ req) {
        ApiResponse<ReportCreditNewCouponsLineManagementVO> apiResponse = new ApiResponse<>();
        req.setInsideSecAccNameList(fixManagerNameListIfNecessary(req.getInsideSecAccNameList()));
        if (CollUtil.isEmpty(req.getInsideSecAccNameList())) {
            return apiResponse.setData(Collections.emptyList());
        }
        Map<String, Object> param = ObjConversionTool.convertMap(req);
        List<EmSort> querySorts = new ArrayList<>();
        if (CollectionUtils.isEmpty(req.getSorts())) {
            EmSort sort = new EmSort("bondMarketCode", true);
            querySorts.add(sort);
        } else {
            querySorts.addAll(req.getSorts());
        }
        ApiResponse<ReportCreditNewCouponsLineManagementBO> res;
        if (TimeTool.CURRENT.getCunrrentyyyyMMdd().equals(req.getBizDate())) {
            param.put("sorts", querySorts);
            res = ApiUtil.getCustomPage(param, new SelectCallBack() {
                @Override
                public List<ReportCreditNewCouponsLineManagementBO> doInSelect(Map map) {
                    return ReportCreditNewCouponsLineManagementDao.Instance.selectPage(param);
                }
            });
        } else {
            querySorts = querySorts
                    .stream()
                    .filter(sort -> !tnPropertyNameList.contains(sort.getPropertyName()))
                    .collect(Collectors.toList());
            param.put("sorts", querySorts);
            res = ApiUtil.getCustomPage(param, new SelectCallBack() {
                @Override
                public List<ReportCreditNewCouponsLineManagementBO> doInSelect(Map map) {
                    return ReportCreditNewCouponsLineManagementDao.Instance.selectHisPage(param);
                }
            });
        }
        List<ReportCreditNewCouponsLineManagementVO> data = res.getData().stream().map(ReportCreditNewCouponsLineManagementVO::toVO).collect(Collectors.toList());
        return apiResponse.setData(data).setCount(res.getCount()).setOk();
    }

    /**
     * 如有必要修复投资经理 防止越权
     *
     * @param managerNameList List<String>
     * @return List<String>
     */
    private List<String> fixManagerNameListIfNecessary(List<String> managerNameList) {
        String userName = UserSessionUtil.getUser().getUserName();
        List<String> authorityInsideSecAccNameList = accountTreeService.getAuthorityName(userName);
        if (CollUtil.isEmpty(authorityInsideSecAccNameList)) {
            return Collections.emptyList();
        }
        if (!CollUtil.containsAll(authorityInsideSecAccNameList, managerNameList)) {
            LogTool.warn(BUSINESS_NAME + "该账户参数越权 自营账号:{}, 有权参数:{}, 入参:{}",
                    userName, authorityInsideSecAccNameList, managerNameList);
            return new ArrayList<>(CollUtil.intersection(authorityInsideSecAccNameList, managerNameList));
        }
        return CollUtil.isNotEmpty(managerNameList) ? managerNameList : authorityInsideSecAccNameList;
    }

    private void saveCouponsLineManagement(List<PositionDataVO> positionDataByInsideSecList, Map<String, Set<String>> groupNameMap,
                                           String bizDate) {
        StopWatch watch = new StopWatch();
        watch.start();

        List<String> codeList = positionDataByInsideSecList
                .stream()
                .map(PositionDataVO::getBondMarketCode)
                .filter(StrUtil::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());
        List<BondFxIndicatoraRespDTO> fxIndicatoraRespDTOS = bondChoiceService.queryBondFxIndicatoraFromChoiceByEnBale(codeList,
                DateUtil.parse(bizDate), false);
        //债券及其最新面值
        Map<String, String> bondNewestParValueMap = fxIndicatoraRespDTOS.stream()
                .filter(x -> StrUtil.isNotEmpty(x.getSecuCode()) && StrUtil.isNotEmpty(x.getNewestParValue()))
                .collect(Collectors.toMap(BondFxIndicatoraRespDTO::getSecuCode, BondFxIndicatoraRespDTO::getNewestParValue, (oldVal, newVal) -> newVal));

        //查询担保人和发行人的内评及授信额度、以及境外债的所属主体的内评及授信额度
        List<String> instNameList = positionDataByInsideSecList
                .stream()
                .filter(x -> !OTHER.equals(x.getMarket()))
                .map(PositionDataVO::getInstName)
                .distinct()
                .collect(Collectors.toList());
        List<String> underWriterList = positionDataByInsideSecList
                .stream()
                .filter(x -> !OTHER.equals(x.getMarket()))
                .map(PositionDataVO::getUnderWriter)
                .distinct()
                .collect(Collectors.toList());
        List<String> offshoreBondManCompanyList = positionDataByInsideSecList
                .stream()
                .filter(x -> OTHER.equals(x.getMarket()))
                .map(PositionDataVO::getManCompany)
                .distinct()
                .collect(Collectors.toList());
        List<String> allList = new ArrayList<>(CollUtil.unionDistinct(instNameList, underWriterList, offshoreBondManCompanyList));
        List<List<String>> partitionAllList = Lists.partition(allList, 1000);

        Map<String, ManCompanyCreditBondReportVO> manCompanyCreditBondReportMap = partitionAllList
                .stream()
                .map(ManCompanyCreditBondReportBaseInfoDao.Instance::selectListWithCompanies)
                .flatMap(List::stream)
                .peek(this::calcPtQuota)
                .collect(Collectors.toMap(ManCompanyCreditBondReportVO::getCompany, Function.identity(), (oldVal, newVal) -> newVal));
        //个券额度信息
        List<ReportCreditNewCouponsLineManagementDO> resDOList = new CopyOnWriteArrayList<>();
        List<ReportCreditNewCouponsLineManagementTnDO> resTnDOList = new CopyOnWriteArrayList<>();

        //根据所属区域，汇总生成T+0持仓面额 T+N持仓面额 (亿元),T+0占用额度 T+N占用额度 (亿元),注意单位
        Map<String, ReportCreditNewRegionManagementDO> reportCreditNewRegionManagementDOMap = new ConcurrentHashMap<>();
        Map<String, ReportCreditNewRegionLimitManagementDO> reportCreditNewRegionLimitManagementDOMap = new ConcurrentHashMap<>();

        List<ReportParamConfigDO> occupancyQuotaConfigDOS = ReportParamConfigDao.Instance.getByBizType(BIZ_TYPE);

        Map<String, String> occupancyQuotaMap = occupancyQuotaConfigDOS
                .stream()
                .collect(Collectors.toMap(ReportParamConfigDO::getParamName, ReportParamConfigDO::getParamValue));

        positionDataByInsideSecList.parallelStream().forEach(x -> {
            String bondMarketCode = x.getBondMarketCode();
            String bondNewestParValue = bondNewestParValueMap.get(bondMarketCode);
            ReportCreditNewCouponsLineManagementDO resDO = ReportCreditNewCouponsLineManagementDO.builder()
                    .bizDate(Integer.valueOf(bizDate))
                    .bondCode(x.getBondCode())
                    .bondMarketCode(x.getBondMarketCode())
                    .bondName(x.getBondName())
                    .market(x.getMarket())
                    .insideSecAccName(x.getInsideSecAccName())
                    .insideSecAccNo(x.getInsideSecAccNo())
                    .instname(x.getInstName())
                    .underwriter(x.getUnderWriter())
                    .bondSecondType(x.getBondSecondType())
                    .strIsmdebt(x.getStrIsmDebt())
                    .perpetualbond(x.getPerpetualBond())
                    .yyManCompanyType(x.getYySubjectType())
                    .yyGovernment(x.getYyaArea())
                    .yyAdminLevel(x.getYyaAdminLevel())
                    .remTerm(x.getRemTerm())
                    .remTermYear(x.getRemTermYear())
                    .positionAmountT0(getPositionAmountTn(BigDecimalUtil.tryParseDecimal(x.getTActualTrustee()), bondNewestParValue, x.getAvailableAmount(), x.getMarket()))
                    .availableAmountT0(getAvailableAmountTn(x.getAvailableAmount()))
                    .build();

            ReportCreditNewCouponsLineManagementTnDO resTnDO = ReportCreditNewCouponsLineManagementTnDO.builder()
                    .bizDate(Integer.valueOf(bizDate))
                    .bondMarketCode(x.getBondMarketCode())
                    .insideSecAccNo(x.getInsideSecAccNo())
                    .build();
            //设置组别
            if (CollUtil.isNotEmpty(groupNameMap)) {
                Set<String> marketMakingInsideSecAccNames = groupNameMap.get(ManualBondGroupTypeEnum.MARKET_MAKING.getName());
                Set<String> proprietaryInsideSecAccNames = groupNameMap.get(ManualBondGroupTypeEnum.PROPRIETARY.getName());
                if (CollUtil.isNotEmpty(marketMakingInsideSecAccNames) && marketMakingInsideSecAccNames.contains(x.getInsideSecAccName())) {
                    resDO.setGroupName(ManualBondGroupTypeEnum.MARKET_MAKING.getName());
                } else if (CollUtil.isNotEmpty(proprietaryInsideSecAccNames) && proprietaryInsideSecAccNames.contains(x.getInsideSecAccName())) {
                    resDO.setGroupName(ManualBondGroupTypeEnum.PROPRIETARY.getName());
                }
            }
            String groupName = resDO.getGroupName();
            //发行人内评信息
            ManCompanyCreditBondReportVO instNameCreditBondReportVO = StrUtil.isNotBlank(x.getInstName()) ? manCompanyCreditBondReportMap.get(x.getInstName()) : null;
            //担保人内评信息
            ManCompanyCreditBondReportVO underWriterCreditBondReportVO = StrUtil.isNotBlank(x.getUnderWriter()) ? manCompanyCreditBondReportMap.get(x.getUnderWriter()) : null;
            resDO.setInstnameEvaluation(Optional.ofNullable(instNameCreditBondReportVO).map(ManCompanyCreditBondReportVO::getRiskInternalEvaluation).orElse(null));
            resDO.setUnderwriterEvaluation(Optional.ofNullable(underWriterCreditBondReportVO).map(ManCompanyCreditBondReportVO::getRiskInternalEvaluation).orElse(null));

            InvestableResultBO eligibilityResult = investableQuotaQueryService.checkCanInvest(resDO.getBondMarketCode());
            if (OTHER.equals(x.getMarket())) {
                String manCompany = x.getManCompany();
                ManCompanyCreditBondReportVO manCompanyCreditBondReportVO = StrUtil.isNotBlank(manCompany) ? manCompanyCreditBondReportMap.get(manCompany) : null;
                resDO.setManCompany(manCompany);
                resDO.setManCompanyEvaluation(Optional.ofNullable(manCompanyCreditBondReportVO).map(ManCompanyCreditBondReportVO::getRiskInternalEvaluation).orElse(null));
                resDO.setManCompanyLimit(calcManCompanyLimit(manCompanyCreditBondReportVO, groupName));
                resDO.setRegion(x.getRegion());
                if (eligibilityResult.isCanInvest()) {
                    resDO.setHighLight(CommonConstants.NO_HIGH_LIGHT);
                } else {
                    resDO.setHighLight(CommonConstants.HIGH_LIGHT);
                }
            } else {
                //不是境外债，根据个券准入规则判断可入库后，再确定所属主体(担保人内评>发行人内评，占用担保人额度,否则占用发行人)
                if (eligibilityResult.isCanInvest()) {
                    if (validCompany(underWriterCreditBondReportVO, groupName) && validCompany(instNameCreditBondReportVO, groupName)) {
                        if (underWriterCreditBondReportVO.getRiskInternalEvaluation() > instNameCreditBondReportVO.getRiskInternalEvaluation()) {
                            setManCompanyInfo(resDO, underWriterCreditBondReportVO, groupName);
                        } else if (underWriterCreditBondReportVO.getRiskInternalEvaluation().equals(instNameCreditBondReportVO.getRiskInternalEvaluation())) {
                            setManCompanyInfo(resDO, instNameCreditBondReportVO, groupName);
                        } else {
                            LogTool.error(BUSINESS_NAME + "担保人内评<发行人内评：担保人：{}，发行人：{}", underWriterCreditBondReportVO, instNameCreditBondReportVO);
                            setManCompanyInfo(resDO, instNameCreditBondReportVO, groupName);
                        }
                    } else if (validCompany(underWriterCreditBondReportVO, groupName)) {
                        setManCompanyInfo(resDO, underWriterCreditBondReportVO, groupName);
                    } else if (validCompany(instNameCreditBondReportVO, groupName)) {
                        setManCompanyInfo(resDO, instNameCreditBondReportVO, groupName);
                    }
                    resDO.setHighLight(CommonConstants.NO_HIGH_LIGHT);
                } else {
                    resDO.setHighLight(CommonConstants.HIGH_LIGHT);
                }
                //只有债券为城投时，也就是Choice接口[主体类型(YY)]输出“城投”或空值时，所属区域字段非空
                if (BondInfoDataBO.isCtz(resDO.getYyManCompanyType())) {
                    resDO.setRegion(investableQuotaQueryService.parseArea(x.getYyaArea(), x.getYyaAdminLevel()));
                }
            }
            String quotaFactor;
            // 查询【额度折算系数维护】-额度折算系数
            if (ChoiceBondTypeEnum.NCDS.getName().equals(x.getRefBondType())) {
                quotaFactor = occupancyQuotaMap.get(INTERBANK_DEPOSIT_PARAM_NAME);
            } else if (ChoiceBondTypeEnum.ABS.getName().equals(x.getRefBondType())
                    || BooleanEnum.TRUE.getName().equals(x.getPerpetualBond())
                    || BooleanEnum.TRUE.getName().equals(x.getStrIsmDebt())) {
                quotaFactor = occupancyQuotaMap.get(SUBORDINATED_ANDABS_PARAM_NAME);
            } else {
                quotaFactor = occupancyQuotaMap.get(GENERAL_PARAM_NAME);
            }
            resDO.setQuotaFactor(quotaFactor);
            resDO.setOccupationQuotaT0(getOccupationQuotaTn(resDO.getPositionAmountT0(), quotaFactor));
            if (!OTHER.equals(x.getMarket())) {
                if (ObjectUtil.isNotNull(x.getTrusteeTnVO())) {
                    resTnDO.setPositionAmountT1(getPositionAmountTn(x.getTrusteeTnVO().getT1TrusteeAmount(), bondNewestParValue, null, null));
                    resTnDO.setPositionAmountT2(getPositionAmountTn(x.getTrusteeTnVO().getT2TrusteeAmount(), bondNewestParValue, null, null));
                    resTnDO.setPositionAmountT3(getPositionAmountTn(x.getTrusteeTnVO().getT3TrusteeAmount(), bondNewestParValue, null, null));
                    resTnDO.setPositionAmountT4(getPositionAmountTn(x.getTrusteeTnVO().getT4TrusteeAmount(), bondNewestParValue, null, null));
                    resTnDO.setPositionAmountT5(getPositionAmountTn(x.getTrusteeTnVO().getT5TrusteeAmount(), bondNewestParValue, null, null));
                    resTnDO.setPositionAmountT6(getPositionAmountTn(x.getTrusteeTnVO().getT6TrusteeAmount(), bondNewestParValue, null, null));
                    resTnDO.setPositionAmountT7(getPositionAmountTn(x.getTrusteeTnVO().getT7TrusteeAmount(), bondNewestParValue, null, null));
                }
                resTnDO.setAvailableAmountT1(getAvailableAmountTn(x.getAvailableTnVO().getT1AvailableAmount()));
                resTnDO.setAvailableAmountT2(getAvailableAmountTn(x.getAvailableTnVO().getT2AvailableAmount()));
                resTnDO.setAvailableAmountT3(getAvailableAmountTn(x.getAvailableTnVO().getT3AvailableAmount()));
                resTnDO.setAvailableAmountT4(getAvailableAmountTn(x.getAvailableTnVO().getT4AvailableAmount()));
                resTnDO.setAvailableAmountT5(getAvailableAmountTn(x.getAvailableTnVO().getT5AvailableAmount()));
                resTnDO.setAvailableAmountT6(getAvailableAmountTn(x.getAvailableTnVO().getT6AvailableAmount()));
                resTnDO.setAvailableAmountT7(getAvailableAmountTn(x.getAvailableTnVO().getT7AvailableAmount()));
                resTnDO.setOccupationQuotaT1(getOccupationQuotaTn(resTnDO.getPositionAmountT1(), quotaFactor));
                resTnDO.setOccupationQuotaT2(getOccupationQuotaTn(resTnDO.getPositionAmountT2(), quotaFactor));
                resTnDO.setOccupationQuotaT3(getOccupationQuotaTn(resTnDO.getPositionAmountT3(), quotaFactor));
                resTnDO.setOccupationQuotaT4(getOccupationQuotaTn(resTnDO.getPositionAmountT4(), quotaFactor));
                resTnDO.setOccupationQuotaT5(getOccupationQuotaTn(resTnDO.getPositionAmountT5(), quotaFactor));
                resTnDO.setOccupationQuotaT6(getOccupationQuotaTn(resTnDO.getPositionAmountT6(), quotaFactor));
                resTnDO.setOccupationQuotaT7(getOccupationQuotaTn(resTnDO.getPositionAmountT7(), quotaFactor));
            }
            resDO.setEID(RandomUtil.randomUUID());
            resTnDO.setEID(RandomUtil.randomUUID());
            resDOList.add(resDO);
            resTnDOList.add(resTnDO);
            //区域信息
            if (StrUtil.isNotBlank(resDO.getRegion())) {
                reportCreditNewRegionManagementDOMap.compute(resDO.getRegion(), (k, v) -> v == null ? ReportCreditNewRegionManagementDO
                        .builder()
                        .bizDate(resDO.getBizDate())
                        .region(resDO.getRegion())
                        .positionAmountT0(BigDecimalUtil.divide(resDO.getPositionAmountT0(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum()))
                        .positionAmountT1(BigDecimalUtil.divide(resTnDO.getPositionAmountT1(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum()))
                        .positionAmountT2(BigDecimalUtil.divide(resTnDO.getPositionAmountT2(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum()))
                        .positionAmountT3(BigDecimalUtil.divide(resTnDO.getPositionAmountT3(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum()))
                        .positionAmountT4(BigDecimalUtil.divide(resTnDO.getPositionAmountT4(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum()))
                        .positionAmountT5(BigDecimalUtil.divide(resTnDO.getPositionAmountT5(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum()))
                        .positionAmountT6(BigDecimalUtil.divide(resTnDO.getPositionAmountT6(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum()))
                        .positionAmountT7(BigDecimalUtil.divide(resTnDO.getPositionAmountT7(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum()))
                        .occupationQuotaT0(BigDecimalUtil.divide(resDO.getOccupationQuotaT0(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum()))
                        .occupationQuotaT1(BigDecimalUtil.divide(resTnDO.getOccupationQuotaT1(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum()))
                        .occupationQuotaT2(BigDecimalUtil.divide(resTnDO.getOccupationQuotaT2(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum()))
                        .occupationQuotaT3(BigDecimalUtil.divide(resTnDO.getOccupationQuotaT3(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum()))
                        .occupationQuotaT4(BigDecimalUtil.divide(resTnDO.getOccupationQuotaT4(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum()))
                        .occupationQuotaT5(BigDecimalUtil.divide(resTnDO.getOccupationQuotaT5(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum()))
                        .occupationQuotaT6(BigDecimalUtil.divide(resTnDO.getOccupationQuotaT6(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum()))
                        .occupationQuotaT7(BigDecimalUtil.divide(resTnDO.getOccupationQuotaT7(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum()))
                        .build() : addPositionAmountAndOccupationQuota(v, resDO, resTnDO));

                reportCreditNewRegionLimitManagementDOMap.putIfAbsent(resDO.getRegion(), ReportCreditNewRegionLimitManagementDO.builder()
                        .region(resDO.getRegion())
                        .build());
            }
        });

        //查询【区域额度管理】-【内评及授信维护】区域内评、区域限额
        List<String> regionList = resDOList
                .stream()
                .map(ReportCreditNewCouponsLineManagementDO::getRegion)
                .filter(StrUtil::isNotBlank)
                .distinct()
                .collect(Collectors.toList());

        Map<String, ReportCreditNewRegionLimitManagementBO> regionLimitManagementVOMap = Lists.partition(regionList, 1000)
                .stream()
                .map(ReportCreditNewRegionLimitManagementDao.Instance::queryList)
                .flatMap(List::stream)
                .collect(Collectors.toMap(ReportCreditNewRegionLimitManagementBO::getRegion, Function.identity(), (oldVal, newVal) -> newVal));

        resDOList.forEach(resDO -> {
            String region = resDO.getRegion();
            if (StrUtil.isNotBlank(region)) {
                ReportCreditNewRegionLimitManagementBO limitManagementVO = regionLimitManagementVOMap.get(region);
                resDO.setRegionLimit(Optional.ofNullable(limitManagementVO).map(ReportCreditNewRegionLimitManagementBO::getRegionLimit).orElse(null));
                resDO.setRegionEvaluation(Optional.ofNullable(limitManagementVO).map(ReportCreditNewRegionLimitManagementBO::getRegionEvaluation).orElse(null));
            }
        });
        reportCreditNewRegionLimitManagementDOMap.forEach((k, v) -> {
            ReportCreditNewRegionLimitManagementBO limitManagementVO = regionLimitManagementVOMap.get(k);
            v.setEID(RandomUtil.randomUUID());
            v.setRegionLimit(Optional.ofNullable(limitManagementVO).map(ReportCreditNewRegionLimitManagementBO::getRegionLimit).orElse(null));
            v.setRegionEvaluation(Optional.ofNullable(limitManagementVO).map(ReportCreditNewRegionLimitManagementBO::getRegionEvaluation).orElse(null));
        });
        reportCreditNewRegionManagementDOMap.forEach((k, v) -> {
            ReportCreditNewRegionLimitManagementBO limitManagementVO = regionLimitManagementVOMap.get(k);
            v.setEID(RandomUtil.randomUUID());
            v.setRegionLimit(Optional.ofNullable(limitManagementVO).map(ReportCreditNewRegionLimitManagementBO::getRegionLimit).orElse(null));
            v.setRegionEvaluation(Optional.ofNullable(limitManagementVO).map(ReportCreditNewRegionLimitManagementBO::getRegionEvaluation).orElse(null));
        });

        watch.stop();
        LogTool.info(BUSINESS_NAME + "计算个券额度管理和区域额度管理耗时：{},个券额度条数{},区域额度条数{}", watch.getTime(), resDOList.size(), reportCreditNewRegionManagementDOMap.size());
        watch.reset();
        watch.start();

        if (CollUtil.isNotEmpty(resDOList)) {
            ReportCreditNewCouponsLineManagementDao.Instance.insertAndDeleteBatchPool(bizDate, resDOList);
        }

        if (CollUtil.isNotEmpty(resTnDOList)) {
            ReportCreditNewCouponsLineManagementTnDao.Instance.insertAndDeleteBatchPool(resTnDOList);
        }

        if (CollUtil.isNotEmpty(reportCreditNewRegionManagementDOMap)) {
            ReportCreditNewRegionManagementDao.Instance.insertAndDeleteBatchPool(bizDate, new ArrayList<>(reportCreditNewRegionManagementDOMap.values()));
        }

        if (CollUtil.isNotEmpty(reportCreditNewRegionLimitManagementDOMap)) {
            List<ReportCreditNewRegionLimitManagementDO> values = new ArrayList<>(reportCreditNewRegionLimitManagementDOMap.values());
            int totalSize = values.size();
            int num = 0;
            int batchSize = 1000;
            for (int i = 0; i < totalSize; i += batchSize) {
                int endIndex = Math.min(i + batchSize, totalSize);
                List<ReportCreditNewRegionLimitManagementDO> batchList = values.subList(i, endIndex);
                ReportCreditNewRegionLimitManagementDao.Instance.mergeList(batchList);
                LogTool.info("[区域内评和额度]第{}次merge，区间范围：{}~{}", num++, i, endIndex);
            }
        }

        watch.stop();
        LogTool.info(BUSINESS_NAME + "保存个券额度管理和区域额度管理耗时：{}", watch.getTime());
    }

    private ReportCreditNewRegionManagementDO addPositionAmountAndOccupationQuota(ReportCreditNewRegionManagementDO v, ReportCreditNewCouponsLineManagementDO resDO, ReportCreditNewCouponsLineManagementTnDO resTnDO) {
        v.setPositionAmountT0(BigDecimalUtil.add(v.getPositionAmountT0(), BigDecimalUtil.divide(resDO.getPositionAmountT0(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum())));
        v.setPositionAmountT1(BigDecimalUtil.add(v.getPositionAmountT1(), BigDecimalUtil.divide(resTnDO.getPositionAmountT1(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum())));
        v.setPositionAmountT2(BigDecimalUtil.add(v.getPositionAmountT2(), BigDecimalUtil.divide(resTnDO.getPositionAmountT2(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum())));
        v.setPositionAmountT3(BigDecimalUtil.add(v.getPositionAmountT3(), BigDecimalUtil.divide(resTnDO.getPositionAmountT3(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum())));
        v.setPositionAmountT4(BigDecimalUtil.add(v.getPositionAmountT4(), BigDecimalUtil.divide(resTnDO.getPositionAmountT4(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum())));
        v.setPositionAmountT5(BigDecimalUtil.add(v.getPositionAmountT5(), BigDecimalUtil.divide(resTnDO.getPositionAmountT5(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum())));
        v.setPositionAmountT6(BigDecimalUtil.add(v.getPositionAmountT6(), BigDecimalUtil.divide(resTnDO.getPositionAmountT6(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum())));
        v.setPositionAmountT7(BigDecimalUtil.add(v.getPositionAmountT7(), BigDecimalUtil.divide(resTnDO.getPositionAmountT7(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum())));
        v.setOccupationQuotaT0(BigDecimalUtil.add(v.getOccupationQuotaT0(), BigDecimalUtil.divide(resDO.getOccupationQuotaT0(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum())));
        v.setOccupationQuotaT1(BigDecimalUtil.add(v.getOccupationQuotaT1(), BigDecimalUtil.divide(resTnDO.getOccupationQuotaT1(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum())));
        v.setOccupationQuotaT2(BigDecimalUtil.add(v.getOccupationQuotaT2(), BigDecimalUtil.divide(resTnDO.getOccupationQuotaT2(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum())));
        v.setOccupationQuotaT3(BigDecimalUtil.add(v.getOccupationQuotaT3(), BigDecimalUtil.divide(resTnDO.getOccupationQuotaT3(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum())));
        v.setOccupationQuotaT4(BigDecimalUtil.add(v.getOccupationQuotaT4(), BigDecimalUtil.divide(resTnDO.getOccupationQuotaT4(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum())));
        v.setOccupationQuotaT5(BigDecimalUtil.add(v.getOccupationQuotaT5(), BigDecimalUtil.divide(resTnDO.getOccupationQuotaT5(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum())));
        v.setOccupationQuotaT6(BigDecimalUtil.add(v.getOccupationQuotaT6(), BigDecimalUtil.divide(resTnDO.getOccupationQuotaT6(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum())));
        v.setOccupationQuotaT7(BigDecimalUtil.add(v.getOccupationQuotaT7(), BigDecimalUtil.divide(resTnDO.getOccupationQuotaT7(), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum())));
        return v;
    }

    private BigDecimal getPositionAmountTn(BigDecimal tActualTrusteeTn, String bondNewestParValue, String availableAmountTn, String market) {
        if (OTHER.equals(market)) {
            //境外债（只算T+0）：T+0持仓面额(万元) = T+0可用面额(万元) =   境外债维护的 持仓面额
            return BigDecimalUtil.divide(BigDecimalUtil.tryParseDecimal(availableAmountTn), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum());
        }
        //T+0持仓面额(万元)= T+0托管量(万元)/100*债券最新面值。
        return BigDecimalUtil.multiply(BigDecimalUtil.divide(tActualTrusteeTn,
                NumberUnitEnum.MILLION.getBigDecimalNum()), BigDecimalUtil.tryParseDecimal(bondNewestParValue));
    }

    private BigDecimal getOccupationQuotaTn(BigDecimal positionAmountTn, String quotaFactor) {
        //T+0占用额度(万元) = T+0持仓面额*额度折算系数 。
        return BigDecimalUtil.multiply(positionAmountTn, BigDecimalUtil.tryParseDecimal(quotaFactor));
    }

    private BigDecimal getAvailableAmountTn(String availableAmountTn) {
        //T+N可用面额(万元)=T+N卖出可用量(万元) 。 境外债（只算T+0）：T+0持仓面额(万元) = T+0可用面额(万元) =   境外债维护的 持仓面额
        return BigDecimalUtil.divide(BigDecimalUtil.tryParseDecimal(availableAmountTn), NumberUnitEnum.TEN_THOUSAND.getBigDecimalNum());
    }

    private void setManCompanyInfo(ReportCreditNewCouponsLineManagementDO resDO, ManCompanyCreditBondReportVO reportVO, String groupName) {
        resDO.setManCompany(reportVO.getCompany());
        resDO.setManCompanyEvaluation(reportVO.getRiskInternalEvaluation());
        resDO.setManCompanyLimit(calcManCompanyLimit(reportVO, groupName));
    }

    private boolean validCompany(ManCompanyCreditBondReportVO reportVO, String groupName) {
        if (ObjectUtil.isNotNull(reportVO) && StrUtil.isNotBlank(reportVO.getCompany()) && ObjectUtil.isNotNull(reportVO.getRiskInternalEvaluation())) {
            if (ManualBondGroupTypeEnum.MARKET_MAKING.getName().equals(groupName) && StrUtil.isNotBlank(reportVO.getPmQuota()) && NumberUtil.isNumber(reportVO.getPmQuota())) {
                return true;
            } else
                return ManualBondGroupTypeEnum.PROPRIETARY.getName().equals(groupName) && StrUtil.isNotBlank(reportVO.getPtQuota()) && NumberUtil.isNumber(reportVO.getPtQuota());
        }
        return false;
    }

    private BigDecimal calcManCompanyLimit(ManCompanyCreditBondReportVO reportVO, String groupName) {
        if (ObjectUtil.isNull(reportVO)) {
            return null;
        }
        if (ManualBondGroupTypeEnum.MARKET_MAKING.getName().equals(groupName)) {
            return BigDecimalUtil.tryParseDecimal(reportVO.getPmQuota());
        } else if (ManualBondGroupTypeEnum.PROPRIETARY.getName().equals(groupName)) {
            return BigDecimalUtil.tryParseDecimal(reportVO.getPtQuota());
        }
        return null;
    }

    private void calcPtQuota(ManCompanyCreditBondReportVO reportVO) {
        BigDecimal fixQuote = BigDecimalUtil.tryParseDecimal(reportVO.getFixQuota(), 2);
        reportVO.setFixQuota(BigDecimalUtil.convertBigDecimalByScale(fixQuote, 2));
        BigDecimal pmQuota = BigDecimalUtil.tryParseDecimal(reportVO.getPmQuota(), 2);
        reportVO.setPmQuota(BigDecimalUtil.convertBigDecimalByScale(pmQuota, 2));
        if (ObjectUtil.isNull(fixQuote) || ObjectUtil.isNull(pmQuota)) {
            return;
        }

        BigDecimal ptQuota = BigDecimalUtil.subtractOptional(fixQuote, pmQuota, 2);
        reportVO.setPtQuota(BigDecimalUtil.convertBigDecimalByScale(ptQuota, 2));
    }

    /**
     * 组装系统内持仓和导入的境外持仓
     * 境外债持仓表里有维护系统里的数据了,将系统内查出来的持仓过滤掉境外债，
     *
     * @param param
     */
    public List<PositionDataVO> assemblePosition(Map<String, Object> param) {
        List<PositionDataVO> resList = new ArrayList<>();
        //查询导入境外持仓，并转会为系统持仓
        List<OffshoreBondPositionDO> bondPositionDOS = OffshoreBondPositionDao.Instance.selectRes(param);
        List<PositionDataVO> importedPositionDataList = bondPositionDOS
                .stream()
                .filter(ObjectUtil::isNotNull)
                .map(reportCreditLineManagementService::convertToPositionData)
                .collect(Collectors.toList());

        //查询系统债券持仓,排除掉中资美债，因为已经通过刷新刷到境外债持仓维护页面了
        List<PositionDataVO> systemPositionDataVOList = PositionDataDao.Instance.queryTrusteeTnAndAvailableTnPositionData(param);
        List<PositionDataVO> filterSystemPositionList = systemPositionDataVOList
                .stream()
                .filter(x -> ObjectUtil.isNotNull(x) && !OTHER.equals(x.getMarket()))
                .collect(Collectors.toList());
        resList.addAll(importedPositionDataList);
        resList.addAll(filterSystemPositionList);
        return resList;
    }

    public void export(ReportCreditNewCouponsLineManagementQueryREQ req, HttpServletResponse response) {
        req.setPageNo(1);
        req.setPageSize(Integer.MAX_VALUE);
        ApiResponse<ReportCreditNewCouponsLineManagementVO> pageRes = queryForPage(req);
        List<ReportCreditNewCouponsLineManagementVO> managementVOApiResponseData = pageRes.getData();
        List<ReportCreditNewCouponsLineManagementExcelVO> data = BeanUtil.copyToList(managementVOApiResponseData,
                ReportCreditNewCouponsLineManagementExcelVO.class);
        data = Optional
                .ofNullable(data)
                .orElse(Collections.emptyList());
        String fileName = "信用债个券额度管理";
        try {
            // 1. 动态构建表头（关键）
            List<List<String>> head = new ArrayList<>();
            head.add(Collections.singletonList("债券代码"));
            head.add(Collections.singletonList("债券简称"));
            head.add(Collections.singletonList("交易市场"));
            head.add(Collections.singletonList("内部证券账户"));
            head.add(Collections.singletonList("发行人"));
            head.add(Collections.singletonList("担保人"));
            head.add(Collections.singletonList("组别"));
            head.add(Collections.singletonList("T+0持仓面额(万元)"));
            if (ObjectUtil.isNotNull(req.getTn())) {
                head.add(Collections.singletonList("T+" + req.getTn() + "持仓面额(万元)"));
            }
            head.add(Collections.singletonList("债券类别"));
            head.add(Collections.singletonList("是否次级"));
            head.add(Collections.singletonList("是否永续"));
            head.add(Collections.singletonList("主体类型"));
            head.add(Collections.singletonList("所属政府"));
            head.add(Collections.singletonList("行政级别"));
            head.add(Collections.singletonList("特殊剩余期限"));
            head.add(Collections.singletonList("剩余期限(行权,年)"));
            head.add(Collections.singletonList("发行人内评"));
            head.add(Collections.singletonList("担保人内评"));
            head.add(Collections.singletonList("所属主体"));
            head.add(Collections.singletonList("主体内评"));
            head.add(Collections.singletonList("所属区域"));
            head.add(Collections.singletonList("区域内评"));
            head.add(Collections.singletonList("主体限额(亿元)"));
            head.add(Collections.singletonList("区域限额（亿元）"));
            head.add(Collections.singletonList("额度折算系数"));
            head.add(Collections.singletonList("T+0占用额度(万元)"));
            if (ObjectUtil.isNotNull(req.getTn())) {
                head.add(Collections.singletonList("T+" + req.getTn() + "占用额度(万元)"));
            }
            head.add(Collections.singletonList("T+0可用面额(万元)"));
            if (ObjectUtil.isNotNull(req.getTn())) {
                head.add(Collections.singletonList("T+" + req.getTn() + "可用面额(万元)"));
            }
            head.add(Collections.singletonList("高亮标识"));

            // 3. 转换数据为Map格式（按索引映射）
            List<Map<Integer, Object>> dataList = new ArrayList<>();
            Integer highlightColumnIndex = null;
            for (ReportCreditNewCouponsLineManagementExcelVO item : data) {
                Map<Integer, Object> rowMap = new HashMap<>();
                int i = 0;
                rowMap.put(i++, item.getBondMarketCode());
                rowMap.put(i++, item.getBondName());
                rowMap.put(i++, item.getMarket());
                rowMap.put(i++, item.getInsideSecAccName());
                rowMap.put(i++, item.getInstname());
                rowMap.put(i++, item.getUnderwriter());
                rowMap.put(i++, item.getGroupName());
                rowMap.put(i++, item.getPositionAmountT0());
                if (ObjectUtil.isNotNull(req.getTn())) {
                    rowMap.put(i++, item.getPositionAmountTN());
                }
                rowMap.put(i++, item.getBondSecondType());
                rowMap.put(i++, item.getStrIsmdebt());
                rowMap.put(i++, item.getPerpetualbond());
                rowMap.put(i++, item.getYyManCompanyType());
                rowMap.put(i++, item.getYyGovernment());
                rowMap.put(i++, item.getYyAdminLevel());
                rowMap.put(i++, item.getRemTerm());
                rowMap.put(i++, item.getRemTermYear());
                rowMap.put(i++, item.getInstnameEvaluation());
                rowMap.put(i++, item.getUnderwriterEvaluation());
                rowMap.put(i++, item.getManCompany());
                rowMap.put(i++, item.getManCompanyEvaluation());
                rowMap.put(i++, item.getRegion());
                rowMap.put(i++, item.getRegionEvaluation());
                rowMap.put(i++, item.getManCompanyLimit());
                rowMap.put(i++, item.getRegionLimit());
                rowMap.put(i++, item.getQuotaFactor());
                rowMap.put(i++, item.getOccupationQuotaT0());
                if (ObjectUtil.isNotNull(req.getTn())) {
                    rowMap.put(i++, item.getOccupationQuotaTN());
                }
                rowMap.put(i++, item.getAvailableAmountT0());
                if (ObjectUtil.isNotNull(req.getTn())) {
                    rowMap.put(i++, item.getAvailableAmountTN());
                }
                rowMap.put(i, item.getHighLight());
                if (ObjectUtil.isNull(highlightColumnIndex)) {
                    highlightColumnIndex = i;
                }
                dataList.add(rowMap);
            }

            FileUtil.responseHeader(response, fileName);

            ExcelWriter writer = EasyExcel.write(response.getOutputStream())
                    .head(head)
                    .registerWriteHandler(new DynamicColumnWidthStyleHandler(req.getTn())) // 注册列宽处理器
                    .registerWriteHandler(new HighlightStyleHandler(highlightColumnIndex)) // 注册高亮处理器
                    .build();

            WriteSheet sheet = EasyExcel.writerSheet(0, req.getBizDate()).build();
            writer.write(dataList, sheet);
            writer.finish();

        } catch (Exception e) {
            LogTool.error(BUSINESS_NAME + "导出失败,{}", e);
        }
    }

    // 高亮处理器实现
    public class HighlightStyleHandler implements RowWriteHandler, SheetWriteHandler {
        // 通过反射获取highLight字段的列索引（避免硬编码）
        private Integer highlightColumnIndex;

        public HighlightStyleHandler(Integer i) {
            this.highlightColumnIndex = i;
        }

        @Override
        public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            if (highlightColumnIndex != null) {
                // 创建 sheet 后立即隐藏列
                writeSheetHolder.getSheet().setColumnHidden(highlightColumnIndex, true);
            }
        }

        @Override
        public void afterRowDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                    Row row, Integer relativeRowIndex, Boolean isHead) {
            if (isHead || highlightColumnIndex == null) return; // 跳过表头和未找到字段

            Cell highLightCell = row.getCell(highlightColumnIndex);
            if (highLightCell != null && CellType.NUMERIC.equals(highLightCell.getCellType())) {
                int highLightValue = (int) highLightCell.getNumericCellValue();
                if (highLightValue == CommonConstants.HIGH_LIGHT) {
                    // 设置黄色背景
                    CellStyle style = writeSheetHolder.getSheet().getWorkbook().createCellStyle();
                    style.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
                    style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

                    // 整行应用样式
                    for (Cell cell : row) {
                        cell.setCellStyle(style);
                    }
                }
            }
        }
    }

    public class DynamicColumnWidthStyleHandler implements CellWriteHandler {
        private final Map<Integer, Integer> columnWidthMap = new HashMap<>();
        private boolean initialized = false;

        public DynamicColumnWidthStyleHandler(Integer tn) {
            initColumnWidthMap(tn);
        }

        private void initColumnWidthMap(Integer tn) {
            Field[] fields = ReportCreditNewCouponsLineManagementExcelVO.class.getDeclaredFields();
            int i = 0;
            for (Field field : fields) {
                ColumnWidth columnWidth = field.getAnnotation(ColumnWidth.class);
                String name = field.getName();
                if (!tnPropertyNameList.contains(name) || ObjectUtil.isNotNull(tn)) {
                    if (columnWidth != null) {
                        // 获取注解中的 index 值
                        int width = columnWidth.value();
                        columnWidthMap.put(i++, width * 256); // 转换为POI单位（1/256字符）
                    }
                }
            }
            initialized = true;
        }

        @Override
        public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                     Row row, Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {
            if (!initialized || !isHead || row.getRowNum() != 0) return;

            Sheet sheet = writeSheetHolder.getSheet();
            Integer width = columnWidthMap.get(columnIndex);
            if (width != null) {
                sheet.setColumnWidth(columnIndex, width);
            } else {
                sheet.setColumnWidth(columnIndex, 15 * 256);
            }
        }
    }

}