<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.eastmoney.ptms.dao.mapper.o32.O32StockInfoMapper">

    <select id="selectConvertibleBondAmount" resultType="com.eastmoney.ptms.data.ficc.VO.StockInfoVO">
        select
        to_char(a.l_date) as lDate,
        a.l_fund_id as lFundId,
        b.vc_report_code as vcReportCode,
        b.vc_stock_name as vcStockName,
        a.vc_inter_code as vcInterCode,
        b.c_stock_type as cStockType,
        sum(a.l_current_amount) as lCurrentAmount ,
        sum(a.l_buy_amount) as lBuyAmount,
        sum(a.l_sale_amount) as lSaleAmount,
        sum(a.en_buy_balance) as enBuyBalance,
        sum(a.en_sale_balance) as enSaleBalance,
        sum(a.en_buy_balance - a.en_sale_balance) as enAbsbuyBalance,
        sum(a.en_buy_fee) as enBuyFee,
        sum(a.en_untransfered_invest) as enUntransferedInvest,
        nvl(max(h.en_bond_interest), 0.00) as enBondInterest
        from TRADE.TUNITSTOCK a, TRADE.TSTOCKINFO b, TRADE.TFUNDINFO d, TRADE.tBONDPROPERTY h
        where
        a.vc_inter_code = b.vc_inter_code and a.l_date=b.l_date
        and a.l_fund_id=d.l_fund_id
        and a.l_date = h.l_date(+) and a.vc_inter_code = h.vc_inter_code(+)
        <if test="fundIdList != null and fundIdList.size() > 0">
            and a.l_fund_id in
            <foreach collection="fundIdList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and ( b.c_stock_type ='5' or (b.c_stock_type ='F' and b.vc_inter_code in ('511380','511180')))
        and exists (select * from TRADE.TCOMBI where l_combi_id=a.l_basecombi_id and c_combi_status not in ('2','3'))
        and ((a.l_buy_amount + a.l_sale_amount) <![CDATA[<>]]> 0 or a.l_current_amount <![CDATA[<>]]> 0 )
        group by
        a.l_date,a.l_fund_id, b.vc_report_code,b.vc_stock_name,a.vc_inter_code,b.c_stock_type
    </select>
    <select id="selectYesterdayConvertibleBondAmount" resultType="com.eastmoney.ptms.data.ficc.VO.StockInfoVO">
        SELECT
        to_char(a.l_date) as lDate,
        a.l_fund_id as lFundId,
        b.vc_report_code as vcReportCode,
        b.vc_stock_name as vcStockName,
        a.vc_inter_code as vcInterCode,
        b.c_stock_type as cStockType,
        sum(a.l_current_amount) as lCurrentAmount
        from TRADE.THISUNITSTOCK a,
        TRADE.THISSTOCKINFO b
        where a.vc_inter_code = b.vc_inter_code
        and a.l_date = b.l_date
        <if test="fundIdList != null and fundIdList.size() > 0">
            and a.l_fund_id in
            <foreach collection="fundIdList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        and ( b.c_stock_type ='5' or (b.c_stock_type ='F' and b.vc_inter_code in ('511380','511180')))
        and exists (select *
        from TRADE.TCOMBI
        where l_combi_id = a.l_basecombi_id and c_combi_status not in ('2', '3'))
        and ((a.l_buy_amount + a.l_sale_amount)  <![CDATA[<>]]> 0 or a.l_current_amount  <![CDATA[<>]]> 0)
        and a.l_date = #{lastBizDate}
        group by a.l_date,a.l_fund_id,b.vc_report_code, b.vc_stock_name, a.vc_inter_code,b.c_stock_type
    </select>
</mapper>