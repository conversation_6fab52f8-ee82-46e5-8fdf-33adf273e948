package com.eastmoney.ptms.dao.mapper.o32;

import com.eastmoney.ptms.dao.core.BaseO32Mapper;
import com.eastmoney.ptms.data.po.o32.POStockInfo;
import com.eastmoney.ptms.data.ficc.VO.StockInfoVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * TRAD_TD_TDATE 对应mapper
 *
 * <AUTHOR>
 * @Date 2022/8/15 9:44
 */
public interface O32StockInfoMapper extends BaseO32Mapper<POStockInfo, String> {


    List<StockInfoVO> selectConvertibleBondAmount(@Param("fundIdList") List<String> fundIdList);


    List<StockInfoVO> selectYesterdayConvertibleBondAmount(@Param("lastBizDate")Integer lastBizDate,@Param("fundIdList") List<String> fundIdList);
}
