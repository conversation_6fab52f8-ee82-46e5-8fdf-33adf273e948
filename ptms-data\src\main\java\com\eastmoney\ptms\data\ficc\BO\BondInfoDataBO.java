package com.eastmoney.ptms.data.ficc.BO;

import cn.hutool.core.lang.Opt;
import cn.hutool.core.util.StrUtil;
import com.eastmoney.digi.common.util.BigDecimalUtil;
import com.eastmoney.digi.core.log.LogTool;
import com.eastmoney.ptms.data.common.CommonConstants;
import com.eastmoney.ptms.data.ficc.DO.OffshoreBondPositionDO;
import com.eastmoney.ptms.data.ficc.DTO.choice.BondBaseInfoRespDTO;
import com.eastmoney.ptms.data.ficc.DTO.choice.BondFxIndicatoraRespDTO;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 债券信息和Choice数据的包装类
 *
 * <AUTHOR>
 * @date 2024-12-20
 */
@Data
public class BondInfoDataBO {
    private BondBaseInfoRespDTO bondBaseInfoRespDTO;
    private BondFxIndicatoraRespDTO bondFxIndicatoraRespDTO;
    private boolean isOffshore = false;
    private OffshoreBondPositionDO offshoreBondPositionDO;

    /**
     * 获取剩余期限，行权年
     * 处理逻辑：
     * 1. 纯数字时，直接取
     * 2. 数字后面有+号字符继续有数字，如 0.8849+1+1+1+10.8849 时取第一个 0.8849
     * 3. 如果是已到期则为0
     */
    public BigDecimal getRemainingTermNum() {
        return Opt.ofNullable(bondFxIndicatoraRespDTO)
                .map(BondFxIndicatoraRespDTO::getRemTerm)
                .map(BondInfoDataBO::getRemainingTermNum)
                .orElse(BigDecimal.ZERO);
    }

    public static BigDecimal getRemainingTermNum(String remTerm) {
        return Opt.ofNullable(remTerm)
                .map(BondInfoDataBO::parseRemainingTerm)
                .orElse(BigDecimal.ZERO);
    }

    /**
     * /**
     * 计算天数结果，如果有未识别到的计算逻辑，则默认返回0并告警
     *
     * @param exp
     * @return
     */
    private static BigDecimal parseRemainingTerm(String exp) {
        if (StrUtil.isBlank(exp)) {
            return BigDecimal.ZERO;
        }

        // 处理特殊情况：已到期
        if ("已到期".equals(exp) || "违约".equals(exp)) {
            return BigDecimal.ZERO;
        }

        try {
            String[] split = exp.split("\\+");

            exp = split[0];

            char unit = exp.charAt(exp.length() - 1);
            if (Character.isDigit(unit)) {
                return BigDecimalUtil.tryParseDecimal(exp);
            }
            switch (unit) {
                case CommonConstants.DAY:
                case CommonConstants.DAY_LOWER:
                case CommonConstants.DAY_ZH:
                    return BigDecimalUtil.divide(BigDecimalUtil.tryParseDecimal((exp.substring(0, exp.length() - 1))),
                            BigDecimal.valueOf(CommonConstants.DAYS_OF_ONE_YEAR), 2);
                case CommonConstants.YEAR:
                case CommonConstants.YEAR_LOWER:
                case CommonConstants.YEAR_ZH:
                    return BigDecimalUtil.tryParseDecimal(exp.substring(0, exp.length() - 1));
                case CommonConstants.MONTH:
                case CommonConstants.MONTH_LOWER:
                case CommonConstants.MONTH_ZH:
                    return BigDecimalUtil.divide(BigDecimalUtil.tryParseDecimal((exp.substring(0, exp.length() - 1))),
                            BigDecimal.valueOf(CommonConstants.MONTHS_OF_ONE_YEAR), 2);
                default:
                    LogTool.info("债券信息同步 计算RemTermYear异常，securityTerm：{}", exp);
                    return BigDecimal.ZERO;
            }
        } catch (Exception e) {
            LogTool.info("债券信息同步 计算RemTermYear异常，securityTerm：{}", exp, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 是否跳升利率
     * RATE_CLAUSECONTENT
     */
    public boolean isInterestRateJump() {
        String ratingContent = Opt.ofNullable(bondBaseInfoRespDTO)
                .map(BondBaseInfoRespDTO::getRateClausecontent)
                .get();
        return isInterestRateJump(ratingContent);
    }

    public static boolean isInterestRateJump(String ratingContent) {
        if (StrUtil.isBlank(ratingContent)) {
            return false;
        }
        return StrUtil.contains(ratingContent, "跳升利率") || StrUtil.contains(ratingContent, "跃升利率");
    }

    /**
     * 是否含回售条款
     */
    public boolean isHasPutOption() {
        String strTermstype = Opt.ofNullable(bondBaseInfoRespDTO)
                .map(BondBaseInfoRespDTO::getStrTermstype)
                .get();
        return isHasPutOption(strTermstype);
    }

    public static boolean isHasPutOption(String strTermstype) {
        if (StrUtil.isBlank(strTermstype)) {
            return false;
        }
        return StrUtil.contains(strTermstype, "回售");
    }

    /**
     * 是否由城投主体发行
     */
    public boolean isCtz() {
        if (!isOffshore()) {
            String subjectType = bondBaseInfoRespDTO.getYySubjectType();
            return isCtz(subjectType);
        } else {
            return Opt.ofNullable(offshoreBondPositionDO)
                    .map(a -> a.getIsItACityInvestment() == 1)
                    .orElse(false);
        }
    }

    public static boolean isCtz(String subjectType) {
        return StrUtil.isBlank(subjectType) || "城投".equals(subjectType);
    }

    /**
     * 是否永续
     */
    public boolean isPerpetualBond() {
        return isPerpetualBond(bondBaseInfoRespDTO.getPerpetualBond());
    }

    public static boolean isPerpetualBond(String perpetualBond) {
        return "是".equals(perpetualBond);
    }

    /**
     * 是否次级
     */
    public boolean isMdebt() {
        return isMdebt(bondBaseInfoRespDTO.getStrIsMdebt());
    }

    public static boolean isMdebt(String strIsMdebt) {
        return "是".equals(strIsMdebt);
    }

    /**
     * 转换为债券资格检查信息对象
     *
     * @param instNameEvaluation       发行人内评等级
     * @param wtyCompanyInternalRating 担保人内评等级
     * @return BondEligibilityInfo对象
     */
    public BondEligibilityInfo toBondEligibilityInfo(Integer instNameEvaluation, Integer wtyCompanyInternalRating) {
        return new BondEligibilityInfo(
                this.isOffshore(),
                this.getBondBaseInfoRespDTO()
                        .getStrDczcYjfl2021(),
                this.getBondBaseInfoRespDTO()
                        .getStrDczcEjfl2021(),
                this.getBondBaseInfoRespDTO()
                        .getInstName(),
                this.getBondBaseInfoRespDTO()
                        .getWtyCompname(),
                this.isPerpetualBond(),
                this.isMdebt(),
                this.getRemainingTermNum(),
                this.isInterestRateJump(),
                this.isHasPutOption(),
                this.isCtz(),
                instNameEvaluation,
                wtyCompanyInternalRating
        );
    }

}
