#==============================================
# application
#==============================================
server.port=10090

app.appId=em-ptms-service
app.version=1.0.0
app.serverId=0
app.modules=com.eastmoney.ptms.api.core.ApiModule
app.ompProdId=213000156487312987

#==============================================
# spring
#==============================================
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB
#spring.data.mongodb.uri=@mongodb.uri@


#===============================================
# ptms-hub
#===============================================
hub.http.url=http://*************:10091
hub.http.ipWhiteList=

#===============================================
# 客户管理
#===============================================
cust.localFilePath =/opt/digi-services/em-ptms-service/uploads/

#===============================================
# 交易簿记
#===============================================
choice.url=@choice.url@
choice.source=@choice.source@
choice.client=@choice.client@

#===============================================
# spring.redis
#===============================================
spring.redis.password=
spring.redis.timeout=20000
spring.redis.database=0
spring.redis.lettuce.pool.max-active=8
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=0
spring.redis.lettuce.pool.max-wait=1
spring.redis.sentinel.master=mymaster
#spring.redis.sentinel.nodes=***********:26379,***********:26379,***********:26379
spring.redis.sentinel.nodes=***********:26379
#spring.redis.host=***********
#spring.redis.port=6379

#===============================================
# ptms-hub
#===============================================
hub.http.url=http://*************:10091
hub.http.ipWhiteList=


以下配置直接新增

#===============================================
# 固收
#===============================================
ht.url=http://***********:8086/api
ht.client.userid=10000
ht.client.pwd=
#===============================================
# choice
#===============================================
choice.bondChoiceService.enabled=true

#===============================================
# mongodb
#===============================================
spring.data.mongodb.uri=***********************************************************************************************

spring.session.store-type=redis

ht.client.default.manager=17246

feign.client.config.default.ReadTimeout=60000
feign.client.config.default.ConnectTimeout=60000

#自营固收系统url
ptms.service.host=http://**********:20090/#/fixed-income

#===============================================
# \u62A5\u8868\u901A\u7528\u914D\u7F6E\u987A\u5E8F\u914D\u7F6E
#===============================================
report.config.departmentName.order=\u56fa\u6536\u6295\u8d44,\u4e8c\u7ea7\u8d44\u91d1\u6c60,\u9500\u552e\u4ea4\u6613
report.config.groupName.order=\u4e3b\u8d26\u6237,\u5229\u7387\u6295\u8d44,\u505a\u5e02\u4ea4\u6613,\u53ef\u8f6c\u503a\u4ea4\u6613,\u6c11\u751f\u52a0\u94f6,\u56fd\u541b\u8d44\u7ba1,\u4e8c\u7ea7\u8d44\u91d1\u6c60,\u9500\u552e\u4ea4\u6613
#===============================================
#managerPosition
#===============================================
position.manager.distribution.name=\u878d\u8d44\u4e13\u6237-\u4e3b\u8d26\u6237



#===============================================
# 资管计划内部证券账户
#===============================================
report.pal.detail.exclude.insideSecAccName=报价回购-民生加银财富1号,国泰君安君得3409号,国泰君安得3485号


#===============================================
# \u6295\u8d44\u7ecf\u7406\u865a\u62df\u6301\u4ed3\u6392\u9664\u8d26\u6237
#===============================================
position.manager.distribution.exclude=\u5229\u7387\u503a\u4ea4\u6613\u6237-\u9ad8\u626c

#===============================================
#\u8d22\u52a1\u8d44\u91d1\u6c60\u8d28\u62bc\u5f0f\u56de\u8d2d\u4e13\u6237
#===============================================
position.manager.distribution.financial.name=\u878d\u8d44\u4e13\u6237-\u81ea\u8425\u6295\u8d44\u501f\u5238\u878d\u8d44
#===============================================
#\u8d22\u52a1\u8d44\u91d1\u6c60\u8282\u70b9\u4e0b\u5185\u90e8\u8bc1\u5238\u8d26\u6237
#===============================================
financial.department.list=\u8d22\u52a1\u8d44\u91d1,\u878d\u5238\u5356\u7a7a-\u8d22\u52a1\u8d44\u91d1,\u878d\u5238\u4e13\u6237-\u8d22\u52a1\u8d44\u91d1,\u878d\u8d44\u4e13\u6237-\u8d22\u52a1\u8d44\u91d1,\u878d\u8d44\u4e13\u6237-\u81ea\u8425\u6295\u8d44\u501f\u5238\u878d\u8d44

server.tomcat.basedir=/opt/digi-services/em-ptms-service/ptmsTemp

spring.pmms.redis.password=
spring.pmms.redis.timeout=30000
spring.pmms.redis.database=8
spring.pmms.redis.lettuce.pool.max-active=32
spring.pmms.redis.lettuce.pool.max-idle=32
spring.pmms.redis.lettuce.pool.min-idle=0
spring.pmms.redis.lettuce.pool.max-wait=-1
spring.pmms.redis.sentinel.master=mymaster
spring.pmms.redis.sentinel.nodes=***********:26379,***********:26380,***********:26381

pmms.service.ip=**********
pmms.service.httpPort=18080
pmms.service.quoteSnapshotPath=/serviceApi/api/shsz/getQuoteSnapshot

o32.enable=false
o32.fund-id=8020,8001

#===============================================
#回购偏离度明细过滤报表通用配置所属科目
#===============================================
back.offset.detail.filter.report.config.subject=\u4ea7\u54c1\u6237

app.captchaPath=false

ccdcStatus.redis.waitSeconds=15
ccdcStatus.delay.time=10
ccdcStatus.handleAmpInfo.enable=true

report.cisp.f3070.bizMonth=202412
report.cisp.f3070.endBalance.03=0
report.cisp.f3070.endBalance.0301=0
report.cisp.f3070.endBalance.030101=0
report.cisp.f3070.endBalance.030102=0
report.cisp.f3070.endBalance.0302=0
report.cisp.f3070.endBalance.030201=0
report.cisp.f3070.endBalance.030202=0
report.cisp.f3070.yearTotalIncrease.03=0
report.cisp.f3070.yearTotalIncrease.0301=0
report.cisp.f3070.yearTotalIncrease.030101=0
report.cisp.f3070.yearTotalIncrease.030102=0
report.cisp.f3070.yearTotalIncrease.0302=0
report.cisp.f3070.yearTotalIncrease.030201=0
report.cisp.f3070.yearTotalIncrease.030202=0